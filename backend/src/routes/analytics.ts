import { Router, Request, Response } from 'express';
import { asyncHandler } from '../middleware/errorHandler.js';
import { PriceService } from '../services/PriceService.js';
import type { 
  CoinData, 
  MarketPrice, 
  PortfolioSummary, 
  CoinSummary, 
  ApiResponse 
} from '../../../shared/types.js';

const router = Router();

// Initialize price service
const priceService = new PriceService();

// Mock coins data (replace with database in production)
let coins: CoinData[] = [];

// GET /api/analytics/portfolio-summary - Get portfolio summary
router.get('/portfolio-summary', asyncHandler(async (req: Request, res: Response) => {
  if (coins.length === 0) {
    const emptyResponse: ApiResponse<PortfolioSummary> = {
      success: true,
      data: {
        totalInvested: 0,
        totalCurrentValue: 0,
        totalPnlAmount: 0,
        totalPnlPercent: 0,
        coinsCount: 0,
        lastUpdated: new Date()
      },
      message: 'No coins in portfolio'
    };
    
    res.json(emptyResponse);
    return;
  }

  // Get current prices for all coins
  const symbols = coins.map(coin => coin.symbol);
  const prices = await priceService.fetchMultiplePrices(symbols);
  const priceMap = new Map(prices.map(p => [p.symbol, p.price]));

  let totalInvested = 0;
  let totalCurrentValue = 0;

  // Calculate totals
  coins.forEach(coin => {
    const marketPrice = priceMap.get(coin.symbol) || 0;
    
    // Calculate total invested for this coin
    let coinTotalInvested = coin.initialAmountDollars;
    let totalCoinQty = coin.initialAmountDollars / coin.initialEntryPrice;

    coin.repurchases.forEach(rep => {
      if (rep.price > 0 && rep.amount > 0) {
        coinTotalInvested += rep.amount;
        totalCoinQty += rep.amount / rep.price;
      }
    });

    const currentValue = totalCoinQty * marketPrice;
    
    totalInvested += coinTotalInvested;
    totalCurrentValue += currentValue;
  });

  const totalPnlAmount = totalCurrentValue - totalInvested;
  const totalPnlPercent = totalInvested > 0 ? (totalPnlAmount / totalInvested) * 100 : 0;

  const portfolioSummary: PortfolioSummary = {
    totalInvested,
    totalCurrentValue,
    totalPnlAmount,
    totalPnlPercent,
    coinsCount: coins.length,
    lastUpdated: new Date()
  };

  const response: ApiResponse<PortfolioSummary> = {
    success: true,
    data: portfolioSummary,
    message: 'Portfolio summary calculated'
  };

  res.json(response);
}));

// GET /api/analytics/coins-summary - Get detailed coins summary
router.get('/coins-summary', asyncHandler(async (req: Request, res: Response) => {
  if (coins.length === 0) {
    const response: ApiResponse<CoinSummary[]> = {
      success: true,
      data: [],
      message: 'No coins in portfolio'
    };
    
    res.json(response);
    return;
  }

  // Get current prices for all coins
  const symbols = coins.map(coin => coin.symbol);
  const prices = await priceService.fetchMultiplePrices(symbols);
  const priceMap = new Map(prices.map(p => [p.symbol, p.price]));

  const coinsSummary: CoinSummary[] = coins.map(coin => {
    const marketPrice = priceMap.get(coin.symbol) || 0;
    
    // Calculate coin metrics
    let totalInvestedAmount = coin.initialAmountDollars;
    let totalCoinQty = coin.initialAmountDollars / coin.initialEntryPrice;

    coin.repurchases.forEach(rep => {
      if (rep.price > 0 && rep.amount > 0) {
        totalInvestedAmount += rep.amount;
        totalCoinQty += rep.amount / rep.price;
      }
    });

    const averageEntryPrice = totalInvestedAmount / totalCoinQty;
    const currentPortfolioValue = totalCoinQty * marketPrice;
    const pnlAmount = currentPortfolioValue - totalInvestedAmount;
    const pnlPercent = totalInvestedAmount > 0 ? (pnlAmount / totalInvestedAmount) * 100 : 0;

    return {
      symbol: coin.symbol,
      totalCoinQty,
      totalInvestedAmount,
      averageEntryPrice,
      marketPrice,
      currentPortfolioValue,
      pnlAmount,
      pnlPercent,
      error: marketPrice === 0 ? 'Price not available' : undefined
    };
  });

  const response: ApiResponse<CoinSummary[]> = {
    success: true,
    data: coinsSummary,
    message: `Calculated summary for ${coinsSummary.length} coins`
  };

  res.json(response);
}));

// GET /api/analytics/performance - Get performance analytics
router.get('/performance', asyncHandler(async (req: Request, res: Response) => {
  if (coins.length === 0) {
    const response: ApiResponse<any> = {
      success: true,
      data: {
        profitableCoins: 0,
        losingCoins: 0,
        neutralCoins: 0,
        bestPerformer: null,
        worstPerformer: null,
        totalReturn: 0,
        averageReturn: 0
      },
      message: 'No coins in portfolio'
    };
    
    res.json(response);
    return;
  }

  // Get current prices for all coins
  const symbols = coins.map(coin => coin.symbol);
  const prices = await priceService.fetchMultiplePrices(symbols);
  const priceMap = new Map(prices.map(p => [p.symbol, p.price]));

  const performances = coins.map(coin => {
    const marketPrice = priceMap.get(coin.symbol) || 0;
    
    let totalInvestedAmount = coin.initialAmountDollars;
    let totalCoinQty = coin.initialAmountDollars / coin.initialEntryPrice;

    coin.repurchases.forEach(rep => {
      if (rep.price > 0 && rep.amount > 0) {
        totalInvestedAmount += rep.amount;
        totalCoinQty += rep.amount / rep.price;
      }
    });

    const currentValue = totalCoinQty * marketPrice;
    const pnlAmount = currentValue - totalInvestedAmount;
    const pnlPercent = totalInvestedAmount > 0 ? (pnlAmount / totalInvestedAmount) * 100 : 0;

    return {
      symbol: coin.symbol,
      pnlAmount,
      pnlPercent,
      invested: totalInvestedAmount,
      currentValue
    };
  });

  const profitableCoins = performances.filter(p => p.pnlPercent > 0).length;
  const losingCoins = performances.filter(p => p.pnlPercent < 0).length;
  const neutralCoins = performances.filter(p => p.pnlPercent === 0).length;

  const bestPerformer = performances.reduce((best, current) => 
    current.pnlPercent > best.pnlPercent ? current : best
  );

  const worstPerformer = performances.reduce((worst, current) => 
    current.pnlPercent < worst.pnlPercent ? current : worst
  );

  const totalInvested = performances.reduce((sum, p) => sum + p.invested, 0);
  const totalCurrentValue = performances.reduce((sum, p) => sum + p.currentValue, 0);
  const totalReturn = totalCurrentValue - totalInvested;
  const averageReturn = performances.length > 0 ? 
    performances.reduce((sum, p) => sum + p.pnlPercent, 0) / performances.length : 0;

  const performanceData = {
    profitableCoins,
    losingCoins,
    neutralCoins,
    bestPerformer: {
      symbol: bestPerformer.symbol,
      return: bestPerformer.pnlPercent,
      pnl: bestPerformer.pnlAmount
    },
    worstPerformer: {
      symbol: worstPerformer.symbol,
      return: worstPerformer.pnlPercent,
      pnl: worstPerformer.pnlAmount
    },
    totalReturn,
    averageReturn,
    totalInvested,
    totalCurrentValue
  };

  const response: ApiResponse<any> = {
    success: true,
    data: performanceData,
    message: 'Performance analytics calculated'
  };

  res.json(response);
}));

// GET /api/analytics/allocation - Get portfolio allocation
router.get('/allocation', asyncHandler(async (req: Request, res: Response) => {
  if (coins.length === 0) {
    const response: ApiResponse<any> = {
      success: true,
      data: [],
      message: 'No coins in portfolio'
    };
    
    res.json(response);
    return;
  }

  // Calculate total invested
  let totalInvested = 0;
  const allocations = coins.map(coin => {
    let coinTotalInvested = coin.initialAmountDollars;
    coin.repurchases.forEach(rep => {
      if (rep.price > 0 && rep.amount > 0) {
        coinTotalInvested += rep.amount;
      }
    });
    totalInvested += coinTotalInvested;
    return { symbol: coin.symbol, invested: coinTotalInvested };
  });

  // Calculate percentages
  const allocationData = allocations.map(allocation => ({
    symbol: allocation.symbol,
    invested: allocation.invested,
    percentage: totalInvested > 0 ? (allocation.invested / totalInvested) * 100 : 0
  })).sort((a, b) => b.percentage - a.percentage);

  const response: ApiResponse<any> = {
    success: true,
    data: allocationData,
    message: 'Portfolio allocation calculated'
  };

  res.json(response);
}));

// GET /api/analytics/targets - Get targets analysis
router.get('/targets', asyncHandler(async (req: Request, res: Response) => {
  if (coins.length === 0) {
    const response: ApiResponse<any> = {
      success: true,
      data: [],
      message: 'No coins in portfolio'
    };
    
    res.json(response);
    return;
  }

  // Get current prices for all coins
  const symbols = coins.map(coin => coin.symbol);
  const prices = await priceService.fetchMultiplePrices(symbols);
  const priceMap = new Map(prices.map(p => [p.symbol, p.price]));

  const targetsAnalysis = coins.map(coin => {
    const marketPrice = priceMap.get(coin.symbol) || 0;
    
    // Calculate average entry price
    let totalInvested = coin.initialAmountDollars;
    let totalQty = coin.initialAmountDollars / coin.initialEntryPrice;

    coin.repurchases.forEach(rep => {
      if (rep.price > 0 && rep.amount > 0) {
        totalInvested += rep.amount;
        totalQty += rep.amount / rep.price;
      }
    });

    const averageEntryPrice = totalInvested / totalQty;

    // Calculate target prices
    const targets = {
      tp1: averageEntryPrice * (1 + coin.targets.tp1 / 100),
      tp2: averageEntryPrice * (1 + coin.targets.tp2 / 100),
      tp3: averageEntryPrice * (1 + coin.targets.tp3 / 100),
      sl: averageEntryPrice * (1 - coin.targets.sl / 100)
    };

    // Check which targets are reached
    const targetStatus = {
      tp1: marketPrice >= targets.tp1,
      tp2: marketPrice >= targets.tp2,
      tp3: marketPrice >= targets.tp3,
      sl: marketPrice <= targets.sl
    };

    return {
      symbol: coin.symbol,
      currentPrice: marketPrice,
      averageEntryPrice,
      targets,
      targetStatus,
      targetPercentages: coin.targets
    };
  });

  const response: ApiResponse<any> = {
    success: true,
    data: targetsAnalysis,
    message: 'Targets analysis calculated'
  };

  res.json(response);
}));

export default router;
