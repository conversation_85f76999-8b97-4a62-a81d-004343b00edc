import { Router, Request, Response } from 'express';
import { asyncHandler } from '../middleware/errorHandler.js';
import { getRequestStats, getRecentErrors } from '../middleware/requestLogger.js';
import { getRateLimiterStats } from '../middleware/rateLimiter.js';

const router = Router();

// Basic health check
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: {
      seconds: Math.floor(uptime),
      formatted: formatUptime(uptime)
    },
    memory: {
      rss: `${Math.round(memoryUsage.rss / 1024 / 1024)} MB`,
      heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`,
      heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
      external: `${Math.round(memoryUsage.external / 1024 / 1024)} MB`
    },
    node: {
      version: process.version,
      platform: process.platform,
      arch: process.arch
    },
    environment: process.env.NODE_ENV || 'development'
  });
}));

// Detailed health check with system stats
router.get('/detailed', asyncHandler(async (req: Request, res: Response) => {
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  const requestStats = getRequestStats();
  const rateLimiterStats = getRateLimiterStats();
  const recentErrors = getRecentErrors(10);

  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    system: {
      uptime: {
        seconds: Math.floor(uptime),
        formatted: formatUptime(uptime)
      },
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
        arrayBuffers: memoryUsage.arrayBuffers
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      node: {
        version: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid
      }
    },
    application: {
      name: 'Crypto Portfolio Manager API',
      version: '2.0.0',
      environment: process.env.NODE_ENV || 'development',
      port: process.env.PORT || 3001
    },
    requests: {
      total: requestStats.totalRequests,
      methods: requestStats.methodStats,
      status: requestStats.statusStats,
      averageResponseTime: Math.round(requestStats.averageResponseTime),
      topEndpoints: requestStats.topEndpoints.slice(0, 5)
    },
    rateLimiting: rateLimiterStats,
    recentErrors: recentErrors.map(error => ({
      timestamp: error.timestamp,
      method: error.method,
      url: error.url,
      statusCode: error.statusCode,
      error: error.error
    }))
  });
}));

// Readiness check (for Kubernetes/Docker)
router.get('/ready', asyncHandler(async (req: Request, res: Response) => {
  // Check if all required services are available
  const checks = {
    server: true, // Server is running if we reach this point
    memory: checkMemoryUsage(),
    uptime: process.uptime() > 10 // Server has been running for at least 10 seconds
  };

  const isReady = Object.values(checks).every(check => check === true);

  res.status(isReady ? 200 : 503).json({
    success: isReady,
    status: isReady ? 'ready' : 'not ready',
    timestamp: new Date().toISOString(),
    checks
  });
}));

// Liveness check (for Kubernetes/Docker)
router.get('/live', asyncHandler(async (req: Request, res: Response) => {
  // Basic liveness check - if we can respond, we're alive
  res.json({
    success: true,
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
}));

// API version and endpoints info
router.get('/info', asyncHandler(async (req: Request, res: Response) => {
  res.json({
    success: true,
    api: {
      name: 'Crypto Portfolio Manager API',
      version: '2.0.0',
      description: 'Backend API for managing cryptocurrency portfolios',
      author: 'المهندس معتز [تداولجي]',
      contact: '@TadawulGY'
    },
    endpoints: {
      health: {
        '/api/health': 'Basic health check',
        '/api/health/detailed': 'Detailed system information',
        '/api/health/ready': 'Readiness probe',
        '/api/health/live': 'Liveness probe',
        '/api/health/info': 'API information'
      },
      coins: {
        'GET /api/coins': 'Get all coins',
        'POST /api/coins': 'Create new coin',
        'GET /api/coins/:symbol': 'Get specific coin',
        'PUT /api/coins/:symbol': 'Update coin',
        'DELETE /api/coins/:symbol': 'Delete coin'
      },
      prices: {
        'GET /api/prices': 'Get all cached prices',
        'POST /api/prices': 'Get prices for specific symbols',
        'GET /api/prices/:symbol': 'Get price for specific symbol'
      },
      analytics: {
        'GET /api/analytics/portfolio-summary': 'Get portfolio summary',
        'GET /api/analytics/coins-summary': 'Get coins summary'
      },
      data: {
        'GET /api/data/export': 'Export portfolio data',
        'POST /api/data/import': 'Import portfolio data'
      }
    },
    features: [
      'Real-time price updates',
      'WebSocket support',
      'Rate limiting',
      'Request logging',
      'Error handling',
      'Data import/export',
      'Portfolio analytics'
    ],
    timestamp: new Date().toISOString()
  });
}));

// Helper functions
function formatUptime(uptime: number): string {
  const days = Math.floor(uptime / 86400);
  const hours = Math.floor((uptime % 86400) / 3600);
  const minutes = Math.floor((uptime % 3600) / 60);
  const seconds = Math.floor(uptime % 60);

  const parts = [];
  if (days > 0) parts.push(`${days}d`);
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  parts.push(`${seconds}s`);

  return parts.join(' ');
}

function checkMemoryUsage(): boolean {
  const memoryUsage = process.memoryUsage();
  const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
  const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024;
  
  // Consider unhealthy if heap usage is over 90%
  const heapUsagePercent = (heapUsedMB / heapTotalMB) * 100;
  return heapUsagePercent < 90;
}

export default router;
