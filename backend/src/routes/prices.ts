import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { asyncHandler, createValidationError, createNotFoundError } from '../middleware/errorHandler.js';
import { pricesRateLimiter } from '../middleware/rateLimiter.js';
import { PriceService } from '../services/PriceService.js';
import type { MarketPrice, ApiResponse } from '../../../shared/types.js';

const router = Router();

// Initialize price service
const priceService = new PriceService();

// Apply rate limiting to all price routes
router.use(pricesRateLimiter);

// Validation schemas
const symbolsSchema = z.object({
  symbols: z.array(z.string().min(1)).min(1, 'At least one symbol is required')
});

const symbolParamSchema = z.string().min(1, 'Symbol is required');

// GET /api/prices - Get all cached prices
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const cachedPrices = priceService.getAllCachedPrices();

  const response: ApiResponse<MarketPrice[]> = {
    success: true,
    data: cachedPrices,
    message: `Retrieved ${cachedPrices.length} cached prices`
  };

  res.json(response);
}));

// POST /api/prices - Get prices for specific symbols
router.post('/', asyncHandler(async (req: Request, res: Response) => {
  const validationResult = symbolsSchema.safeParse(req.body);
  
  if (!validationResult.success) {
    throw createValidationError(
      `Validation failed: ${validationResult.error.errors.map(e => e.message).join(', ')}`
    );
  }

  const { symbols } = validationResult.data;
  const upperSymbols = symbols.map(s => s.toUpperCase());

  // Validate symbol formats
  const invalidSymbols = upperSymbols.filter(symbol => !priceService.isValidSymbol(symbol));
  if (invalidSymbols.length > 0) {
    throw createValidationError(`Invalid symbol format: ${invalidSymbols.join(', ')}`);
  }

  // Check cache first
  const cachedPrices: MarketPrice[] = [];
  const symbolsToFetch: string[] = [];

  upperSymbols.forEach(symbol => {
    const cached = priceService.getCachedPrice(symbol);
    if (cached) {
      cachedPrices.push(cached);
    } else {
      symbolsToFetch.push(symbol);
    }
  });

  // Fetch missing prices
  let fetchedPrices: MarketPrice[] = [];
  if (symbolsToFetch.length > 0) {
    fetchedPrices = await priceService.fetchMultiplePrices(symbolsToFetch);
    
    // Add to monitoring if not already monitored
    symbolsToFetch.forEach(symbol => {
      priceService.addMonitoredSymbol(symbol);
    });
  }

  const allPrices = [...cachedPrices, ...fetchedPrices];

  const response: ApiResponse<MarketPrice[]> = {
    success: true,
    data: allPrices,
    message: `Retrieved ${allPrices.length}/${upperSymbols.length} prices (${cachedPrices.length} cached, ${fetchedPrices.length} fetched)`
  };

  res.json(response);
}));

// GET /api/prices/:symbol - Get price for specific symbol
router.get('/:symbol', asyncHandler(async (req: Request, res: Response) => {
  const symbol = req.params.symbol?.toUpperCase();
  
  if (!symbol || !priceService.isValidSymbol(symbol)) {
    throw createValidationError('Invalid symbol format');
  }

  // Check cache first
  let price = priceService.getCachedPrice(symbol);
  
  // If not cached or expired, fetch fresh price
  if (!price) {
    price = await priceService.fetchSinglePrice(symbol);
    
    if (price) {
      // Add to monitoring
      priceService.addMonitoredSymbol(symbol);
    }
  }

  if (!price) {
    throw createNotFoundError(`Price not found for symbol ${symbol}`);
  }

  const response: ApiResponse<MarketPrice> = {
    success: true,
    data: price,
    message: `Retrieved price for ${symbol}`
  };

  res.json(response);
}));

// GET /api/prices/stats/summary - Get price service statistics
router.get('/stats/summary', asyncHandler(async (req: Request, res: Response) => {
  const stats = priceService.getPriceStats();
  const monitoredSymbols = priceService.getMonitoredSymbols();
  const supportedExchanges = priceService.getSupportedExchanges();

  const response: ApiResponse<any> = {
    success: true,
    data: {
      ...stats,
      monitoredSymbols,
      supportedExchanges,
      serviceStatus: 'running'
    },
    message: 'Price service statistics retrieved'
  };

  res.json(response);
}));

// POST /api/prices/monitor - Add symbols to monitoring
router.post('/monitor', asyncHandler(async (req: Request, res: Response) => {
  const validationResult = symbolsSchema.safeParse(req.body);
  
  if (!validationResult.success) {
    throw createValidationError(
      `Validation failed: ${validationResult.error.errors.map(e => e.message).join(', ')}`
    );
  }

  const { symbols } = validationResult.data;
  const upperSymbols = symbols.map(s => s.toUpperCase());

  // Validate symbol formats
  const invalidSymbols = upperSymbols.filter(symbol => !priceService.isValidSymbol(symbol));
  if (invalidSymbols.length > 0) {
    throw createValidationError(`Invalid symbol format: ${invalidSymbols.join(', ')}`);
  }

  // Add to monitoring
  upperSymbols.forEach(symbol => {
    priceService.addMonitoredSymbol(symbol);
  });

  const response: ApiResponse<any> = {
    success: true,
    data: {
      addedSymbols: upperSymbols,
      totalMonitored: priceService.getMonitoredSymbols().length
    },
    message: `Added ${upperSymbols.length} symbols to monitoring`
  };

  res.json(response);
}));

// DELETE /api/prices/monitor/:symbol - Remove symbol from monitoring
router.delete('/monitor/:symbol', asyncHandler(async (req: Request, res: Response) => {
  const symbol = req.params.symbol?.toUpperCase();
  
  if (!symbol) {
    throw createValidationError('Symbol is required');
  }

  priceService.removeMonitoredSymbol(symbol);

  const response: ApiResponse<any> = {
    success: true,
    data: {
      removedSymbol: symbol,
      totalMonitored: priceService.getMonitoredSymbols().length
    },
    message: `Removed ${symbol} from monitoring`
  };

  res.json(response);
}));

// POST /api/prices/refresh - Force refresh prices for monitored symbols
router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  const monitoredSymbols = priceService.getMonitoredSymbols();
  
  if (monitoredSymbols.length === 0) {
    const response: ApiResponse<any> = {
      success: true,
      data: { refreshedPrices: [] },
      message: 'No symbols being monitored'
    };
    
    res.json(response);
    return;
  }

  const refreshedPrices = await priceService.fetchMultiplePrices(monitoredSymbols);

  const response: ApiResponse<MarketPrice[]> = {
    success: true,
    data: refreshedPrices,
    message: `Refreshed ${refreshedPrices.length}/${monitoredSymbols.length} prices`
  };

  res.json(response);
}));

// GET /api/prices/exchanges/supported - Get supported exchanges
router.get('/exchanges/supported', asyncHandler(async (req: Request, res: Response) => {
  const supportedExchanges = priceService.getSupportedExchanges();

  const response: ApiResponse<string[]> = {
    success: true,
    data: supportedExchanges,
    message: `${supportedExchanges.length} exchanges supported`
  };

  res.json(response);
}));

// POST /api/prices/cache/clear - Clear price cache
router.post('/cache/clear', asyncHandler(async (req: Request, res: Response) => {
  priceService.clearExpiredCache();

  const response: ApiResponse<any> = {
    success: true,
    data: { message: 'Cache cleared' },
    message: 'Price cache cleared successfully'
  };

  res.json(response);
}));

export default router;
