import { Router, Request, Response } from 'express';
import multer from 'multer';
import { z } from 'zod';
import { asyncHand<PERSON>, createValidationError } from '../middleware/errorHandler.js';
import { dataRateLimiter } from '../middleware/rateLimiter.js';
import type { CoinData, ExportData, ApiResponse } from '../../../shared/types.js';

const router = Router();

// Apply rate limiting to all data routes
router.use(dataRateLimiter);

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 1
  },
  fileFilter: (req, file, cb) => {
    // Accept only JSON and TXT files
    if (file.mimetype === 'application/json' || 
        file.mimetype === 'text/plain' ||
        file.originalname.endsWith('.json') ||
        file.originalname.endsWith('.txt')) {
      cb(null, true);
    } else {
      cb(new Error('Only JSON and TXT files are allowed'));
    }
  }
});

// Mock coins data (replace with database in production)
let coins: CoinData[] = [];

// Validation schemas
const coinSchema = z.object({
  symbol: z.string().min(1),
  initialEntryPrice: z.number().positive(),
  initialAmountDollars: z.number().positive(),
  repurchases: z.array(z.object({
    price: z.number().min(0),
    amount: z.number().min(0)
  })).optional().default([]),
  targets: z.object({
    tp1: z.number().min(0).optional().default(0),
    tp2: z.number().min(0).optional().default(0),
    tp3: z.number().min(0).optional().default(0),
    sl: z.number().min(0).optional().default(0)
  }).optional().default({})
});

// GET /api/data/export - Export portfolio data
router.get('/export', asyncHandler(async (req: Request, res: Response) => {
  const { format = 'json' } = req.query;

  if (coins.length === 0) {
    const response: ApiResponse<any> = {
      success: false,
      error: 'No data to export',
      message: 'Portfolio is empty'
    };
    
    res.status(404).json(response);
    return;
  }

  const exportData: ExportData = {
    coins,
    summary: {
      totalInvested: 0,
      totalCurrentValue: 0,
      totalPnlAmount: 0,
      totalPnlPercent: 0,
      coinsCount: coins.length,
      lastUpdated: new Date()
    },
    exportDate: new Date(),
    version: '2.0.0'
  };

  // Calculate summary
  let totalInvested = 0;
  coins.forEach(coin => {
    let coinTotalInvested = coin.initialAmountDollars;
    coin.repurchases.forEach(rep => {
      if (rep.price > 0 && rep.amount > 0) {
        coinTotalInvested += rep.amount;
      }
    });
    totalInvested += coinTotalInvested;
  });

  exportData.summary.totalInvested = totalInvested;

  if (format === 'txt') {
    // Export in legacy TXT format
    let txtContent = `# مدير الصفقات الشامل - تصدير البيانات\n`;
    txtContent += `# تاريخ التصدير: ${new Date().toLocaleString('ar-SA')}\n`;
    txtContent += `# عدد العملات: ${coins.length}\n\n`;

    coins.forEach((coin, index) => {
      txtContent += `=== العملة ${index + 1} ===\n`;
      txtContent += `رمز: ${coin.symbol}\n`;
      txtContent += `سعر الدخول: ${coin.initialEntryPrice}\n`;
      txtContent += `المبلغ: ${coin.initialAmountDollars}\n`;
      
      coin.repurchases.forEach((rep, repIndex) => {
        if (rep.price > 0 && rep.amount > 0) {
          txtContent += `تعزيز${repIndex + 1}: ${rep.price}, ${rep.amount}\n`;
        }
      });
      
      txtContent += `هدف1: ${coin.targets.tp1}\n`;
      txtContent += `هدف2: ${coin.targets.tp2}\n`;
      txtContent += `هدف3: ${coin.targets.tp3}\n`;
      txtContent += `وقف: ${coin.targets.sl}\n\n`;
    });

    res.setHeader('Content-Type', 'text/plain; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="portfolio-${new Date().toISOString().split('T')[0]}.txt"`);
    res.send(txtContent);
  } else {
    // Export in JSON format
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="portfolio-${new Date().toISOString().split('T')[0]}.json"`);
    res.json(exportData);
  }
}));

// POST /api/data/import - Import portfolio data
router.post('/import', upload.single('file'), asyncHandler(async (req: Request, res: Response) => {
  if (!req.file) {
    throw createValidationError('No file uploaded');
  }

  const fileContent = req.file.buffer.toString('utf-8');
  let importedCoins: CoinData[] = [];

  try {
    if (req.file.originalname.endsWith('.json')) {
      // Parse JSON file
      const jsonData = JSON.parse(fileContent);
      
      if (jsonData.coins && Array.isArray(jsonData.coins)) {
        importedCoins = jsonData.coins;
      } else if (Array.isArray(jsonData)) {
        importedCoins = jsonData;
      } else {
        throw new Error('Invalid JSON format');
      }
    } else if (req.file.originalname.endsWith('.txt')) {
      // Parse TXT file (legacy format)
      importedCoins = parseTxtFile(fileContent);
    } else {
      throw createValidationError('Unsupported file format');
    }

    // Validate imported data
    const validationResults = importedCoins.map((coin, index) => {
      try {
        const validatedCoin = coinSchema.parse(coin);
        return { index, success: true, coin: validatedCoin };
      } catch (error) {
        return { 
          index, 
          success: false, 
          error: error instanceof z.ZodError ? error.errors[0]?.message : 'Validation failed',
          coin 
        };
      }
    });

    const validCoins = validationResults
      .filter(result => result.success)
      .map(result => result.coin as CoinData);

    const errors = validationResults
      .filter(result => !result.success)
      .map(result => ({
        index: result.index,
        symbol: result.coin?.symbol || 'unknown',
        error: result.error
      }));

    // Process import based on strategy
    const { strategy = 'merge' } = req.body;
    let importStats = {
      total: importedCoins.length,
      valid: validCoins.length,
      invalid: errors.length,
      created: 0,
      updated: 0,
      skipped: 0
    };

    if (strategy === 'replace') {
      // Replace all existing coins
      coins = validCoins.map(coin => ({
        ...coin,
        id: generateId(),
        createdAt: new Date(),
        updatedAt: new Date()
      }));
      importStats.created = validCoins.length;
    } else {
      // Merge with existing coins
      validCoins.forEach(importedCoin => {
        const existingIndex = coins.findIndex(c => c.symbol === importedCoin.symbol);
        
        if (existingIndex !== -1) {
          // Update existing coin
          coins[existingIndex] = {
            ...coins[existingIndex],
            ...importedCoin,
            id: coins[existingIndex]!.id,
            createdAt: coins[existingIndex]!.createdAt,
            updatedAt: new Date()
          };
          importStats.updated++;
        } else {
          // Create new coin
          coins.push({
            ...importedCoin,
            id: generateId(),
            createdAt: new Date(),
            updatedAt: new Date()
          });
          importStats.created++;
        }
      });
    }

    const response: ApiResponse<any> = {
      success: true,
      data: {
        stats: importStats,
        errors: errors.length > 0 ? errors : undefined
      },
      message: `Import completed: ${importStats.created} created, ${importStats.updated} updated, ${importStats.invalid} errors`
    };

    res.json(response);

  } catch (error) {
    throw createValidationError(`Failed to parse file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}));

// POST /api/data/backup - Create backup
router.post('/backup', asyncHandler(async (req: Request, res: Response) => {
  const backup = {
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    data: {
      coins,
      metadata: {
        totalCoins: coins.length,
        createdAt: new Date(),
        source: 'api-backup'
      }
    }
  };

  const response: ApiResponse<any> = {
    success: true,
    data: backup,
    message: 'Backup created successfully'
  };

  res.json(response);
}));

// POST /api/data/restore - Restore from backup
router.post('/restore', asyncHandler(async (req: Request, res: Response) => {
  const { data: backupData, confirm = false } = req.body;

  if (!confirm) {
    throw createValidationError('Restore operation requires confirmation');
  }

  if (!backupData || !backupData.data || !Array.isArray(backupData.data.coins)) {
    throw createValidationError('Invalid backup data format');
  }

  const previousCount = coins.length;
  coins = backupData.data.coins.map((coin: any) => ({
    ...coin,
    updatedAt: new Date()
  }));

  const response: ApiResponse<any> = {
    success: true,
    data: {
      previousCount,
      restoredCount: coins.length,
      timestamp: new Date()
    },
    message: `Restored ${coins.length} coins from backup`
  };

  res.json(response);
}));

// DELETE /api/data/clear - Clear all data
router.delete('/clear', asyncHandler(async (req: Request, res: Response) => {
  const { confirm = false } = req.body;

  if (!confirm) {
    throw createValidationError('Clear operation requires confirmation');
  }

  const clearedCount = coins.length;
  coins = [];

  const response: ApiResponse<any> = {
    success: true,
    data: { clearedCount },
    message: `Cleared ${clearedCount} coins from portfolio`
  };

  res.json(response);
}));

// Helper functions
function parseTxtFile(content: string): CoinData[] {
  const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));
  const coins: CoinData[] = [];
  let currentCoin: Partial<CoinData> | null = null;

  for (const line of lines) {
    const trimmedLine = line.trim();
    
    if (trimmedLine.startsWith('رمز:')) {
      // Save previous coin if exists
      if (currentCoin && currentCoin.symbol) {
        coins.push(currentCoin as CoinData);
      }
      
      // Start new coin
      currentCoin = {
        symbol: trimmedLine.split(':')[1]?.trim() || '',
        repurchases: Array.from({ length: 10 }, () => ({ price: 0, amount: 0 })),
        targets: { tp1: 0, tp2: 0, tp3: 0, sl: 0 }
      };
    } else if (currentCoin) {
      if (trimmedLine.startsWith('سعر الدخول:')) {
        currentCoin.initialEntryPrice = parseFloat(trimmedLine.split(':')[1]?.trim() || '0') || 0;
      } else if (trimmedLine.startsWith('المبلغ:')) {
        currentCoin.initialAmountDollars = parseFloat(trimmedLine.split(':')[1]?.trim() || '0') || 0;
      } else if (trimmedLine.startsWith('تعزيز')) {
        const match = trimmedLine.match(/تعزيز(\d+):\s*([^,]+),\s*(.+)/);
        if (match) {
          const index = parseInt(match[1] || '1') - 1;
          if (index >= 0 && index < 10) {
            currentCoin.repurchases![index] = {
              price: parseFloat(match[2] || '0') || 0,
              amount: parseFloat(match[3] || '0') || 0
            };
          }
        }
      } else if (trimmedLine.startsWith('هدف1:')) {
        currentCoin.targets!.tp1 = parseFloat(trimmedLine.split(':')[1]?.trim() || '0') || 0;
      } else if (trimmedLine.startsWith('هدف2:')) {
        currentCoin.targets!.tp2 = parseFloat(trimmedLine.split(':')[1]?.trim() || '0') || 0;
      } else if (trimmedLine.startsWith('هدف3:')) {
        currentCoin.targets!.tp3 = parseFloat(trimmedLine.split(':')[1]?.trim() || '0') || 0;
      } else if (trimmedLine.startsWith('وقف:')) {
        currentCoin.targets!.sl = parseFloat(trimmedLine.split(':')[1]?.trim() || '0') || 0;
      }
    }
  }

  // Save last coin
  if (currentCoin && currentCoin.symbol) {
    coins.push(currentCoin as CoinData);
  }

  return coins;
}

function generateId(): string {
  return `coin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export default router;
