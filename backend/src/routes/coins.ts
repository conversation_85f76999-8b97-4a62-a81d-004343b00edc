import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { asyncHand<PERSON>, createValidationError, createNotFoundError } from '../middleware/errorHandler.js';
import type { CoinData, ApiResponse } from '../../../shared/types.js';

const router = Router();

// In-memory storage (replace with database in production)
let coins: CoinData[] = [];

// Validation schemas
const coinSchema = z.object({
  symbol: z.string()
    .min(1, 'Symbol is required')
    .regex(/^[A-Z]+USDT?$/, 'Symbol must end with USDT or USDT'),
  initialEntryPrice: z.number()
    .positive('Initial entry price must be positive'),
  initialAmountDollars: z.number()
    .positive('Initial amount must be positive'),
  repurchases: z.array(z.object({
    price: z.number().min(0),
    amount: z.number().min(0)
  })).optional().default([]),
  targets: z.object({
    tp1: z.number().min(0).optional().default(0),
    tp2: z.number().min(0).optional().default(0),
    tp3: z.number().min(0).optional().default(0),
    sl: z.number().min(0).optional().default(0)
  }).optional().default({})
});

const updateCoinSchema = coinSchema.partial();

// GET /api/coins - Get all coins
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const response: ApiResponse<CoinData[]> = {
    success: true,
    data: coins,
    message: `Retrieved ${coins.length} coins`
  };

  res.json(response);
}));

// GET /api/coins/:symbol - Get specific coin
router.get('/:symbol', asyncHandler(async (req: Request, res: Response) => {
  const { symbol } = req.params;
  const coin = coins.find(c => c.symbol === symbol.toUpperCase());

  if (!coin) {
    throw createNotFoundError(`Coin ${symbol} not found`);
  }

  const response: ApiResponse<CoinData> = {
    success: true,
    data: coin,
    message: `Retrieved coin ${symbol}`
  };

  res.json(response);
}));

// POST /api/coins - Create new coin
router.post('/', asyncHandler(async (req: Request, res: Response) => {
  const validationResult = coinSchema.safeParse(req.body);
  
  if (!validationResult.success) {
    throw createValidationError(
      `Validation failed: ${validationResult.error.errors.map(e => e.message).join(', ')}`
    );
  }

  const coinData = validationResult.data;
  const symbol = coinData.symbol.toUpperCase();

  // Check if coin already exists
  if (coins.some(c => c.symbol === symbol)) {
    throw createValidationError(`Coin ${symbol} already exists`);
  }

  // Create new coin
  const newCoin: CoinData = {
    id: generateId(),
    symbol,
    initialEntryPrice: coinData.initialEntryPrice,
    initialAmountDollars: coinData.initialAmountDollars,
    repurchases: coinData.repurchases || Array.from({ length: 10 }, () => ({ price: 0, amount: 0 })),
    targets: {
      tp1: coinData.targets?.tp1 || 0,
      tp2: coinData.targets?.tp2 || 0,
      tp3: coinData.targets?.tp3 || 0,
      sl: coinData.targets?.sl || 0
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };

  coins.push(newCoin);

  const response: ApiResponse<CoinData> = {
    success: true,
    data: newCoin,
    message: `Coin ${symbol} created successfully`
  };

  res.status(201).json(response);
}));

// PUT /api/coins/:symbol - Update coin
router.put('/:symbol', asyncHandler(async (req: Request, res: Response) => {
  const { symbol } = req.params;
  const coinIndex = coins.findIndex(c => c.symbol === symbol.toUpperCase());

  if (coinIndex === -1) {
    throw createNotFoundError(`Coin ${symbol} not found`);
  }

  const validationResult = updateCoinSchema.safeParse(req.body);
  
  if (!validationResult.success) {
    throw createValidationError(
      `Validation failed: ${validationResult.error.errors.map(e => e.message).join(', ')}`
    );
  }

  const updateData = validationResult.data;
  const existingCoin = coins[coinIndex]!;

  // Update coin
  const updatedCoin: CoinData = {
    ...existingCoin,
    ...updateData,
    symbol: existingCoin.symbol, // Don't allow symbol changes
    updatedAt: new Date()
  };

  coins[coinIndex] = updatedCoin;

  const response: ApiResponse<CoinData> = {
    success: true,
    data: updatedCoin,
    message: `Coin ${symbol} updated successfully`
  };

  res.json(response);
}));

// DELETE /api/coins/:symbol - Delete coin
router.delete('/:symbol', asyncHandler(async (req: Request, res: Response) => {
  const { symbol } = req.params;
  const coinIndex = coins.findIndex(c => c.symbol === symbol.toUpperCase());

  if (coinIndex === -1) {
    throw createNotFoundError(`Coin ${symbol} not found`);
  }

  coins.splice(coinIndex, 1);

  const response: ApiResponse<boolean> = {
    success: true,
    data: true,
    message: `Coin ${symbol} deleted successfully`
  };

  res.json(response);
}));

// POST /api/coins/bulk - Bulk operations
router.post('/bulk', asyncHandler(async (req: Request, res: Response) => {
  const { operation, data } = req.body;

  switch (operation) {
    case 'import':
      if (!Array.isArray(data)) {
        throw createValidationError('Data must be an array for import operation');
      }

      const importResults = [];
      for (const coinData of data) {
        try {
          const validationResult = coinSchema.safeParse(coinData);
          if (validationResult.success) {
            const symbol = validationResult.data.symbol.toUpperCase();
            
            // Check if coin already exists
            const existingIndex = coins.findIndex(c => c.symbol === symbol);
            
            const newCoin: CoinData = {
              id: existingIndex !== -1 ? coins[existingIndex]!.id : generateId(),
              symbol,
              initialEntryPrice: validationResult.data.initialEntryPrice,
              initialAmountDollars: validationResult.data.initialAmountDollars,
              repurchases: validationResult.data.repurchases || Array.from({ length: 10 }, () => ({ price: 0, amount: 0 })),
              targets: {
                tp1: validationResult.data.targets?.tp1 || 0,
                tp2: validationResult.data.targets?.tp2 || 0,
                tp3: validationResult.data.targets?.tp3 || 0,
                sl: validationResult.data.targets?.sl || 0
              },
              createdAt: existingIndex !== -1 ? coins[existingIndex]!.createdAt : new Date(),
              updatedAt: new Date()
            };

            if (existingIndex !== -1) {
              coins[existingIndex] = newCoin;
              importResults.push({ symbol, status: 'updated' });
            } else {
              coins.push(newCoin);
              importResults.push({ symbol, status: 'created' });
            }
          } else {
            importResults.push({ 
              symbol: coinData.symbol || 'unknown', 
              status: 'error', 
              error: validationResult.error.errors[0]?.message 
            });
          }
        } catch (error) {
          importResults.push({ 
            symbol: coinData.symbol || 'unknown', 
            status: 'error', 
            error: 'Processing failed' 
          });
        }
      }

      const response: ApiResponse<any> = {
        success: true,
        data: {
          results: importResults,
          summary: {
            total: data.length,
            created: importResults.filter(r => r.status === 'created').length,
            updated: importResults.filter(r => r.status === 'updated').length,
            errors: importResults.filter(r => r.status === 'error').length
          }
        },
        message: `Bulk import completed`
      };

      res.json(response);
      break;

    case 'clear':
      const clearedCount = coins.length;
      coins = [];
      
      const clearResponse: ApiResponse<any> = {
        success: true,
        data: { clearedCount },
        message: `Cleared ${clearedCount} coins`
      };

      res.json(clearResponse);
      break;

    default:
      throw createValidationError(`Unknown bulk operation: ${operation}`);
  }
}));

// GET /api/coins/stats - Get coins statistics
router.get('/stats/summary', asyncHandler(async (req: Request, res: Response) => {
  const stats = {
    totalCoins: coins.length,
    symbols: coins.map(c => c.symbol),
    createdToday: coins.filter(c => {
      if (!c.createdAt) return false;
      const today = new Date();
      const coinDate = new Date(c.createdAt);
      return coinDate.toDateString() === today.toDateString();
    }).length,
    lastUpdated: coins.length > 0 ? 
      new Date(Math.max(...coins.map(c => new Date(c.updatedAt || c.createdAt || 0).getTime()))) : 
      null
  };

  const response: ApiResponse<any> = {
    success: true,
    data: stats,
    message: 'Coins statistics retrieved'
  };

  res.json(response);
}));

// Helper function to generate unique IDs
function generateId(): string {
  return `coin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export default router;
