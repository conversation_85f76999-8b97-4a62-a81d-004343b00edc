import { Request, Response, NextFunction } from 'express';
import { RateLimiterMemory } from 'rate-limiter-flexible';

// Create different rate limiters for different endpoints
const generalLimiter = new RateLimiterMemory({
  keyGenerator: (req: Request) => req.ip,
  points: 100, // Number of requests
  duration: 60, // Per 60 seconds
  blockDuration: 60, // Block for 60 seconds if limit exceeded
});

const pricesLimiter = new RateLimiterMemory({
  keyGenerator: (req: Request) => req.ip,
  points: 30, // Number of requests
  duration: 60, // Per 60 seconds
  blockDuration: 30, // Block for 30 seconds if limit exceeded
});

const dataLimiter = new RateLimiterMemory({
  keyGenerator: (req: Request) => req.ip,
  points: 10, // Number of requests
  duration: 60, // Per 60 seconds
  blockDuration: 120, // Block for 2 minutes if limit exceeded
});

const authLimiter = new RateLimiterMemory({
  keyGenerator: (req: Request) => req.ip,
  points: 5, // Number of requests
  duration: 60, // Per 60 seconds
  blockDuration: 300, // Block for 5 minutes if limit exceeded
});

// Helper function to create rate limiter middleware
const createRateLimiterMiddleware = (limiter: RateLimiterMemory, errorMessage?: string) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      await limiter.consume(req.ip);
      next();
    } catch (rejRes: any) {
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      
      res.set('Retry-After', String(secs));
      res.status(429).json({
        success: false,
        error: errorMessage || 'Too many requests',
        message: `Rate limit exceeded. Try again in ${secs} seconds.`,
        retryAfter: secs,
        timestamp: new Date().toISOString()
      });
    }
  };
};

// General rate limiter (default)
export const rateLimiter = createRateLimiterMiddleware(
  generalLimiter,
  'Too many requests from this IP'
);

// Prices endpoint rate limiter
export const pricesRateLimiter = createRateLimiterMiddleware(
  pricesLimiter,
  'Too many price requests from this IP'
);

// Data endpoints rate limiter (import/export)
export const dataRateLimiter = createRateLimiterMiddleware(
  dataLimiter,
  'Too many data requests from this IP'
);

// Authentication endpoints rate limiter
export const authRateLimiter = createRateLimiterMiddleware(
  authLimiter,
  'Too many authentication attempts from this IP'
);

// Strict rate limiter for sensitive operations
export const strictRateLimiter = createRateLimiterMiddleware(
  new RateLimiterMemory({
    keyGenerator: (req: Request) => req.ip,
    points: 3, // Number of requests
    duration: 60, // Per 60 seconds
    blockDuration: 600, // Block for 10 minutes if limit exceeded
  }),
  'Too many sensitive operation attempts from this IP'
);

// WebSocket connection rate limiter
export const wsConnectionLimiter = new RateLimiterMemory({
  keyGenerator: (ip: string) => ip,
  points: 5, // Number of connections
  duration: 60, // Per 60 seconds
  blockDuration: 300, // Block for 5 minutes if limit exceeded
});

// Rate limiter for WebSocket messages
export const wsMessageLimiter = new RateLimiterMemory({
  keyGenerator: (clientId: string) => clientId,
  points: 60, // Number of messages
  duration: 60, // Per 60 seconds
  blockDuration: 60, // Block for 1 minute if limit exceeded
});

// Helper function to check WebSocket connection rate limit
export const checkWsConnectionLimit = async (ip: string): Promise<boolean> => {
  try {
    await wsConnectionLimiter.consume(ip);
    return true;
  } catch (rejRes) {
    console.warn(`WebSocket connection rate limit exceeded for IP: ${ip}`);
    return false;
  }
};

// Helper function to check WebSocket message rate limit
export const checkWsMessageLimit = async (clientId: string): Promise<boolean> => {
  try {
    await wsMessageLimiter.consume(clientId);
    return true;
  } catch (rejRes) {
    console.warn(`WebSocket message rate limit exceeded for client: ${clientId}`);
    return false;
  }
};

// Rate limiter stats
export const getRateLimiterStats = () => {
  return {
    general: {
      points: generalLimiter.points,
      duration: generalLimiter.duration,
      blockDuration: generalLimiter.blockDuration
    },
    prices: {
      points: pricesLimiter.points,
      duration: pricesLimiter.duration,
      blockDuration: pricesLimiter.blockDuration
    },
    data: {
      points: dataLimiter.points,
      duration: dataLimiter.duration,
      blockDuration: dataLimiter.blockDuration
    },
    auth: {
      points: authLimiter.points,
      duration: authLimiter.duration,
      blockDuration: authLimiter.blockDuration
    }
  };
};
