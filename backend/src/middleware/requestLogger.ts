import { Request, Response, NextFunction } from 'express';

interface LogEntry {
  timestamp: string;
  method: string;
  url: string;
  ip: string;
  userAgent: string;
  statusCode?: number;
  responseTime?: number;
  contentLength?: number;
  error?: string;
}

class RequestLogger {
  private logs: LogEntry[] = [];
  private maxLogs: number = 1000;

  log(entry: LogEntry): void {
    this.logs.push(entry);
    
    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console log in development
    if (process.env.NODE_ENV === 'development') {
      const { timestamp, method, url, ip, statusCode, responseTime } = entry;
      const status = statusCode ? ` ${statusCode}` : '';
      const time = responseTime ? ` ${responseTime}ms` : '';
      console.log(`[${timestamp}] ${method} ${url} - ${ip}${status}${time}`);
    }
  }

  getLogs(limit?: number): LogEntry[] {
    if (limit) {
      return this.logs.slice(-limit);
    }
    return [...this.logs];
  }

  getStats(): {
    totalRequests: number;
    methodStats: Record<string, number>;
    statusStats: Record<string, number>;
    averageResponseTime: number;
    topIPs: Array<{ ip: string; count: number }>;
    topEndpoints: Array<{ endpoint: string; count: number }>;
  } {
    const methodStats: Record<string, number> = {};
    const statusStats: Record<string, number> = {};
    const ipStats: Record<string, number> = {};
    const endpointStats: Record<string, number> = {};
    let totalResponseTime = 0;
    let responseTimeCount = 0;

    this.logs.forEach(log => {
      // Method stats
      methodStats[log.method] = (methodStats[log.method] || 0) + 1;

      // Status stats
      if (log.statusCode) {
        const statusGroup = `${Math.floor(log.statusCode / 100)}xx`;
        statusStats[statusGroup] = (statusStats[statusGroup] || 0) + 1;
      }

      // IP stats
      ipStats[log.ip] = (ipStats[log.ip] || 0) + 1;

      // Endpoint stats
      const endpoint = `${log.method} ${log.url.split('?')[0]}`;
      endpointStats[endpoint] = (endpointStats[endpoint] || 0) + 1;

      // Response time stats
      if (log.responseTime) {
        totalResponseTime += log.responseTime;
        responseTimeCount++;
      }
    });

    // Top IPs
    const topIPs = Object.entries(ipStats)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([ip, count]) => ({ ip, count }));

    // Top endpoints
    const topEndpoints = Object.entries(endpointStats)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([endpoint, count]) => ({ endpoint, count }));

    return {
      totalRequests: this.logs.length,
      methodStats,
      statusStats,
      averageResponseTime: responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0,
      topIPs,
      topEndpoints
    };
  }

  clearLogs(): void {
    this.logs = [];
  }

  // Get recent errors
  getRecentErrors(limit: number = 50): LogEntry[] {
    return this.logs
      .filter(log => log.error || (log.statusCode && log.statusCode >= 400))
      .slice(-limit);
  }

  // Get slow requests
  getSlowRequests(threshold: number = 1000, limit: number = 50): LogEntry[] {
    return this.logs
      .filter(log => log.responseTime && log.responseTime > threshold)
      .sort((a, b) => (b.responseTime || 0) - (a.responseTime || 0))
      .slice(0, limit);
  }
}

const logger = new RequestLogger();

export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  
  const logEntry: LogEntry = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.originalUrl || req.url,
    ip: req.ip || req.connection.remoteAddress || 'unknown',
    userAgent: req.get('User-Agent') || 'unknown'
  };

  // Override res.end to capture response details
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any, cb?: any) {
    const responseTime = Date.now() - startTime;
    
    logEntry.statusCode = res.statusCode;
    logEntry.responseTime = responseTime;
    logEntry.contentLength = res.get('Content-Length') ? 
      parseInt(res.get('Content-Length')!) : undefined;

    // Log error if status code indicates an error
    if (res.statusCode >= 400) {
      logEntry.error = `HTTP ${res.statusCode}`;
    }

    logger.log(logEntry);

    // Call original end method
    originalEnd.call(this, chunk, encoding, cb);
  };

  next();
};

// Export logger instance for accessing logs
export const getRequestLogs = (limit?: number) => logger.getLogs(limit);
export const getRequestStats = () => logger.getStats();
export const clearRequestLogs = () => logger.clearLogs();
export const getRecentErrors = (limit?: number) => logger.getRecentErrors(limit);
export const getSlowRequests = (threshold?: number, limit?: number) => 
  logger.getSlowRequests(threshold, limit);

// Middleware to add request ID for tracking
export const addRequestId = (req: Request, res: Response, next: NextFunction): void => {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  req.headers['x-request-id'] = requestId;
  res.setHeader('X-Request-ID', requestId);
  next();
};

// Security headers middleware
export const securityHeaders = (req: Request, res: Response, next: NextFunction): void => {
  // Remove sensitive headers
  res.removeHeader('X-Powered-By');
  
  // Add security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Add HSTS in production
  if (process.env.NODE_ENV === 'production') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }
  
  next();
};

// Request size limiter
export const requestSizeLimiter = (maxSize: number = 10 * 1024 * 1024) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const contentLength = req.get('Content-Length');
    
    if (contentLength && parseInt(contentLength) > maxSize) {
      res.status(413).json({
        success: false,
        error: 'Request entity too large',
        message: `Request size exceeds ${maxSize} bytes`,
        maxSize,
        timestamp: new Date().toISOString()
      });
      return;
    }
    
    next();
  };
};
