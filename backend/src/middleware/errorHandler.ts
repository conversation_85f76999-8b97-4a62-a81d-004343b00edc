import { Request, Response, NextFunction } from 'express';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export class CustomError extends Error implements AppError {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorHandler = (
  error: AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let { statusCode = 500, message } = error;

  // Log error details
  console.error('Error occurred:', {
    message: error.message,
    statusCode,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation Error: ' + message;
  } else if (error.name === 'CastError') {
    statusCode = 400;
    message = 'Invalid data format';
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
  } else if (error.name === 'MongoError' && (error as any).code === 11000) {
    statusCode = 409;
    message = 'Duplicate entry';
  }

  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const errorResponse = {
    success: false,
    error: message,
    statusCode,
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method,
    ...(isDevelopment && {
      stack: error.stack,
      details: error
    })
  };

  res.status(statusCode).json(errorResponse);
};

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404 Not Found handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new CustomError(`Route ${req.originalUrl} not found`, 404);
  next(error);
};

// Validation error creator
export const createValidationError = (message: string): CustomError => {
  return new CustomError(message, 400);
};

// Authorization error creator
export const createAuthError = (message: string = 'Unauthorized'): CustomError => {
  return new CustomError(message, 401);
};

// Forbidden error creator
export const createForbiddenError = (message: string = 'Forbidden'): CustomError => {
  return new CustomError(message, 403);
};

// Not found error creator
export const createNotFoundError = (message: string = 'Resource not found'): CustomError => {
  return new CustomError(message, 404);
};

// Conflict error creator
export const createConflictError = (message: string = 'Resource conflict'): CustomError => {
  return new CustomError(message, 409);
};

// Internal server error creator
export const createInternalError = (message: string = 'Internal server error'): CustomError => {
  return new CustomError(message, 500);
};

// Rate limit error creator
export const createRateLimitError = (message: string = 'Too many requests'): CustomError => {
  return new CustomError(message, 429);
};

// Service unavailable error creator
export const createServiceUnavailableError = (message: string = 'Service temporarily unavailable'): CustomError => {
  return new CustomError(message, 503);
};
