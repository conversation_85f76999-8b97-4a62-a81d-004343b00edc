import { WebSocketServer, WebSocket } from 'ws';
import type { 
  WebSocketMessage, 
  PriceUpdateMessage, 
  AlertTriggeredMessage,
  MarketPrice,
  PriceAlert 
} from '../../../shared/types.js';

interface ClientInfo {
  id: string;
  ws: WebSocket;
  subscribedSymbols: Set<string>;
  lastPing: Date;
}

export class WebSocketService {
  private clients: Map<string, ClientInfo> = new Map();
  private wss: WebSocketServer;
  private pingInterval: NodeJS.Timeout | null = null;

  constructor(wss: WebSocketServer) {
    this.wss = wss;
    this.startPingInterval();
  }

  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private startPingInterval(): void {
    // Send ping every 30 seconds to keep connections alive
    this.pingInterval = setInterval(() => {
      this.clients.forEach((client, clientId) => {
        if (client.ws.readyState === WebSocket.OPEN) {
          client.ws.ping();
          client.lastPing = new Date();
        } else {
          this.removeClient(client.ws);
        }
      });
    }, 30000);
  }

  addClient(ws: WebSocket): string {
    const clientId = this.generateClientId();
    const clientInfo: ClientInfo = {
      id: clientId,
      ws,
      subscribedSymbols: new Set(),
      lastPing: new Date()
    };

    this.clients.set(clientId, clientInfo);
    console.log(`WebSocket client added: ${clientId}. Total clients: ${this.clients.size}`);
    
    return clientId;
  }

  removeClient(ws: WebSocket): void {
    for (const [clientId, client] of this.clients.entries()) {
      if (client.ws === ws) {
        this.clients.delete(clientId);
        console.log(`WebSocket client removed: ${clientId}. Total clients: ${this.clients.size}`);
        break;
      }
    }
  }

  handleMessage(ws: WebSocket, data: any): void {
    try {
      const clientInfo = this.getClientByWebSocket(ws);
      if (!clientInfo) {
        console.warn('Received message from unknown client');
        return;
      }

      switch (data.type) {
        case 'subscribe_prices':
          this.handleSubscribePrices(clientInfo, data.symbols || []);
          break;
          
        case 'unsubscribe_prices':
          this.handleUnsubscribePrices(clientInfo, data.symbols || []);
          break;
          
        case 'ping':
          this.handlePing(clientInfo);
          break;
          
        case 'get_status':
          this.handleGetStatus(clientInfo);
          break;
          
        default:
          console.warn(`Unknown message type: ${data.type}`);
          this.sendToClient(clientInfo, {
            type: 'error',
            data: { message: `Unknown message type: ${data.type}` },
            timestamp: new Date()
          });
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
      this.sendToClient(this.getClientByWebSocket(ws)!, {
        type: 'error',
        data: { message: 'Internal server error' },
        timestamp: new Date()
      });
    }
  }

  private getClientByWebSocket(ws: WebSocket): ClientInfo | null {
    for (const client of this.clients.values()) {
      if (client.ws === ws) {
        return client;
      }
    }
    return null;
  }

  private handleSubscribePrices(client: ClientInfo, symbols: string[]): void {
    symbols.forEach(symbol => {
      if (typeof symbol === 'string' && symbol.length > 0) {
        client.subscribedSymbols.add(symbol.toUpperCase());
      }
    });

    this.sendToClient(client, {
      type: 'subscription_updated',
      data: {
        subscribedSymbols: Array.from(client.subscribedSymbols),
        message: `Subscribed to ${symbols.length} symbols`
      },
      timestamp: new Date()
    });

    console.log(`Client ${client.id} subscribed to: ${symbols.join(', ')}`);
  }

  private handleUnsubscribePrices(client: ClientInfo, symbols: string[]): void {
    symbols.forEach(symbol => {
      if (typeof symbol === 'string') {
        client.subscribedSymbols.delete(symbol.toUpperCase());
      }
    });

    this.sendToClient(client, {
      type: 'subscription_updated',
      data: {
        subscribedSymbols: Array.from(client.subscribedSymbols),
        message: `Unsubscribed from ${symbols.length} symbols`
      },
      timestamp: new Date()
    });

    console.log(`Client ${client.id} unsubscribed from: ${symbols.join(', ')}`);
  }

  private handlePing(client: ClientInfo): void {
    client.lastPing = new Date();
    this.sendToClient(client, {
      type: 'pong',
      data: { timestamp: new Date().toISOString() },
      timestamp: new Date()
    });
  }

  private handleGetStatus(client: ClientInfo): void {
    this.sendToClient(client, {
      type: 'status',
      data: {
        clientId: client.id,
        subscribedSymbols: Array.from(client.subscribedSymbols),
        connectedClients: this.clients.size,
        uptime: process.uptime(),
        lastPing: client.lastPing.toISOString()
      },
      timestamp: new Date()
    });
  }

  private sendToClient(client: ClientInfo, message: WebSocketMessage): void {
    if (client.ws.readyState === WebSocket.OPEN) {
      try {
        client.ws.send(JSON.stringify(message));
      } catch (error) {
        console.error(`Error sending message to client ${client.id}:`, error);
        this.removeClient(client.ws);
      }
    }
  }

  // Broadcast price updates to subscribed clients
  broadcastPriceUpdates(prices: MarketPrice[]): void {
    if (prices.length === 0) return;

    const priceMap = new Map(prices.map(p => [p.symbol, p]));

    this.clients.forEach(client => {
      const relevantPrices = prices.filter(price => 
        client.subscribedSymbols.has(price.symbol)
      );

      if (relevantPrices.length > 0) {
        const message: PriceUpdateMessage = {
          type: 'price_update',
          data: relevantPrices,
          timestamp: new Date()
        };

        this.sendToClient(client, message);
      }
    });

    console.log(`Broadcasted ${prices.length} price updates to ${this.clients.size} clients`);
  }

  // Broadcast single price update
  broadcastSinglePriceUpdate(price: MarketPrice): void {
    this.broadcastPriceUpdates([price]);
  }

  // Broadcast alert notifications
  broadcastAlert(alert: PriceAlert): void {
    const message: AlertTriggeredMessage = {
      type: 'alert_triggered',
      data: alert,
      timestamp: new Date()
    };

    this.clients.forEach(client => {
      if (client.subscribedSymbols.has(alert.symbol)) {
        this.sendToClient(client, message);
      }
    });

    console.log(`Broadcasted alert for ${alert.symbol} to subscribed clients`);
  }

  // Broadcast general notifications
  broadcastNotification(message: string, type: 'info' | 'warning' | 'error' = 'info'): void {
    const notification: WebSocketMessage = {
      type: 'notification',
      data: { message, type },
      timestamp: new Date()
    };

    this.clients.forEach(client => {
      this.sendToClient(client, notification);
    });

    console.log(`Broadcasted ${type} notification: ${message}`);
  }

  // Get connection statistics
  getStats(): {
    totalClients: number;
    activeClients: number;
    totalSubscriptions: number;
    uniqueSymbols: Set<string>;
  } {
    let totalSubscriptions = 0;
    const uniqueSymbols = new Set<string>();
    let activeClients = 0;

    this.clients.forEach(client => {
      if (client.ws.readyState === WebSocket.OPEN) {
        activeClients++;
      }
      totalSubscriptions += client.subscribedSymbols.size;
      client.subscribedSymbols.forEach(symbol => uniqueSymbols.add(symbol));
    });

    return {
      totalClients: this.clients.size,
      activeClients,
      totalSubscriptions,
      uniqueSymbols
    };
  }

  // Clean up disconnected clients
  cleanup(): void {
    const disconnectedClients: string[] = [];

    this.clients.forEach((client, clientId) => {
      if (client.ws.readyState !== WebSocket.OPEN) {
        disconnectedClients.push(clientId);
      }
    });

    disconnectedClients.forEach(clientId => {
      this.clients.delete(clientId);
    });

    if (disconnectedClients.length > 0) {
      console.log(`Cleaned up ${disconnectedClients.length} disconnected clients`);
    }
  }

  // Shutdown the service
  shutdown(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }

    // Close all client connections
    this.clients.forEach(client => {
      if (client.ws.readyState === WebSocket.OPEN) {
        client.ws.close(1000, 'Server shutting down');
      }
    });

    this.clients.clear();
    console.log('WebSocket service shut down');
  }
}
