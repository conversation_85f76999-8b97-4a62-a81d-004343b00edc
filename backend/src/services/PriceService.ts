import axios from 'axios';
import cron from 'node-cron';
import type { MarketPrice } from '../../../shared/types.js';

interface PriceSource {
  name: string;
  fetchPrice: (symbol: string) => Promise<number | null>;
}

export class PriceService {
  private priceCache: Map<string, MarketPrice> = new Map();
  private cronJob: cron.ScheduledTask | null = null;
  private monitoredSymbols: Set<string> = new Set();
  private sources: PriceSource[] = [];

  constructor() {
    this.initializeSources();
  }

  private initializeSources(): void {
    // Binance source
    this.sources.push({
      name: 'binance',
      fetchPrice: async (symbol: string) => {
        try {
          const response = await axios.get(
            `https://api.binance.com/api/v3/ticker/price?symbol=${symbol}`,
            { timeout: 5000 }
          );
          return parseFloat(response.data.price);
        } catch (error) {
          console.warn(`Binance price fetch failed for ${symbol}:`, error);
          return null;
        }
      }
    });

    // KuCoin source
    this.sources.push({
      name: 'kucoin',
      fetchPrice: async (symbol: string) => {
        try {
          const kucoinSymbol = symbol.endsWith('USDT') 
            ? `${symbol.slice(0, -4)}-USDT` 
            : symbol;
          
          const response = await axios.get(
            `https://api.kucoin.com/api/v1/market/orderbook/level1?symbol=${kucoinSymbol}`,
            { timeout: 5000 }
          );
          
          if (response.data.code === '200000' && response.data.data?.price) {
            return parseFloat(response.data.data.price);
          }
          return null;
        } catch (error) {
          console.warn(`KuCoin price fetch failed for ${symbol}:`, error);
          return null;
        }
      }
    });

    // Coinbase source (backup)
    this.sources.push({
      name: 'coinbase',
      fetchPrice: async (symbol: string) => {
        try {
          // Convert BTCUSDT to BTC-USD format
          const coinbaseSymbol = symbol.replace('USDT', '-USD');
          
          const response = await axios.get(
            `https://api.coinbase.com/v2/exchange-rates?currency=${coinbaseSymbol.split('-')[0]}`,
            { timeout: 5000 }
          );
          
          const usdRate = response.data.data?.rates?.USD;
          return usdRate ? parseFloat(usdRate) : null;
        } catch (error) {
          console.warn(`Coinbase price fetch failed for ${symbol}:`, error);
          return null;
        }
      }
    });
  }

  async fetchSinglePrice(symbol: string): Promise<MarketPrice | null> {
    for (const source of this.sources) {
      try {
        const price = await source.fetchPrice(symbol);
        if (price !== null && price > 0) {
          const marketPrice: MarketPrice = {
            symbol,
            price,
            source: source.name as any,
            timestamp: new Date()
          };
          
          this.priceCache.set(symbol, marketPrice);
          return marketPrice;
        }
      } catch (error) {
        console.error(`Error fetching price from ${source.name} for ${symbol}:`, error);
      }
    }

    console.error(`Failed to fetch price for ${symbol} from all sources`);
    return null;
  }

  async fetchMultiplePrices(symbols: string[]): Promise<MarketPrice[]> {
    const promises = symbols.map(symbol => this.fetchSinglePrice(symbol));
    const results = await Promise.allSettled(promises);
    
    return results
      .filter((result): result is PromiseFulfilledResult<MarketPrice> => 
        result.status === 'fulfilled' && result.value !== null
      )
      .map(result => result.value);
  }

  getCachedPrice(symbol: string): MarketPrice | null {
    const cached = this.priceCache.get(symbol);
    if (!cached) return null;

    // Check if cache is still valid (5 minutes)
    const now = new Date();
    const cacheAge = now.getTime() - cached.timestamp.getTime();
    const maxAge = 5 * 60 * 1000; // 5 minutes

    if (cacheAge > maxAge) {
      this.priceCache.delete(symbol);
      return null;
    }

    return cached;
  }

  getAllCachedPrices(): MarketPrice[] {
    const now = new Date();
    const validPrices: MarketPrice[] = [];

    for (const [symbol, price] of this.priceCache.entries()) {
      const cacheAge = now.getTime() - price.timestamp.getTime();
      const maxAge = 5 * 60 * 1000; // 5 minutes

      if (cacheAge <= maxAge) {
        validPrices.push(price);
      } else {
        this.priceCache.delete(symbol);
      }
    }

    return validPrices;
  }

  addMonitoredSymbol(symbol: string): void {
    this.monitoredSymbols.add(symbol);
  }

  removeMonitoredSymbol(symbol: string): void {
    this.monitoredSymbols.delete(symbol);
    this.priceCache.delete(symbol);
  }

  getMonitoredSymbols(): string[] {
    return Array.from(this.monitoredSymbols);
  }

  startMonitoring(intervalMinutes: number = 1): void {
    if (this.cronJob) {
      this.cronJob.stop();
    }

    // Run every minute by default
    this.cronJob = cron.schedule(`*/${intervalMinutes} * * * *`, async () => {
      if (this.monitoredSymbols.size === 0) return;

      console.log(`Updating prices for ${this.monitoredSymbols.size} symbols...`);
      
      try {
        const symbols = Array.from(this.monitoredSymbols);
        const prices = await this.fetchMultiplePrices(symbols);
        
        console.log(`Successfully updated ${prices.length}/${symbols.length} prices`);
        
        // Here you could emit WebSocket events to notify clients
        // wsService.broadcastPriceUpdates(prices);
        
      } catch (error) {
        console.error('Error during scheduled price update:', error);
      }
    });

    console.log(`Price monitoring started with ${intervalMinutes} minute interval`);
  }

  stopMonitoring(): void {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
      console.log('Price monitoring stopped');
    }
  }

  // Get price statistics
  getPriceStats(): {
    totalSymbols: number;
    cachedPrices: number;
    monitoredSymbols: number;
    lastUpdate: Date | null;
  } {
    const cachedPrices = this.getAllCachedPrices();
    const lastUpdate = cachedPrices.length > 0 
      ? new Date(Math.max(...cachedPrices.map(p => p.timestamp.getTime())))
      : null;

    return {
      totalSymbols: this.priceCache.size,
      cachedPrices: cachedPrices.length,
      monitoredSymbols: this.monitoredSymbols.size,
      lastUpdate
    };
  }

  // Clear old cache entries
  clearExpiredCache(): void {
    const now = new Date();
    const maxAge = 5 * 60 * 1000; // 5 minutes

    for (const [symbol, price] of this.priceCache.entries()) {
      const cacheAge = now.getTime() - price.timestamp.getTime();
      if (cacheAge > maxAge) {
        this.priceCache.delete(symbol);
      }
    }
  }

  // Validate symbol format
  isValidSymbol(symbol: string): boolean {
    // Basic validation for crypto trading pairs
    const pattern = /^[A-Z]{2,10}USDT?$/;
    return pattern.test(symbol);
  }

  // Get supported exchanges
  getSupportedExchanges(): string[] {
    return this.sources.map(source => source.name);
  }
}
