{"name": "crypto-portfolio-backend", "version": "2.0.0", "description": "Backend API for Crypto Portfolio Manager", "type": "module", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "keywords": ["crypto", "portfolio", "api", "nodejs", "typescript", "express"], "author": "المهندس معتز [تداولجي]", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "zod": "^3.22.4", "axios": "^1.6.2", "ws": "^8.14.2", "node-cron": "^3.0.3", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "rate-limiter-flexible": "^4.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/ws": "^8.5.10", "@types/node": "^20.10.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node-cron": "^3.0.11", "typescript": "^5.3.3", "tsx": "^4.6.2", "vitest": "^1.0.4", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0"}}