# Backend Environment Variables

# Server Configuration
NODE_ENV=development
PORT=3001
HOST=localhost

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173

# Database Configuration (if using database)
DATABASE_URL="file:./dev.db"

# JWT Configuration (if using authentication)
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# External APIs
BINANCE_API_URL=https://api.binance.com/api/v3
KUCOIN_API_URL=https://api.kucoin.com/api/v1
COINBASE_API_URL=https://api.coinbase.com/v2

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Price Service Configuration
PRICE_UPDATE_INTERVAL=60
PRICE_CACHE_TTL=300
MAX_MONITORED_SYMBOLS=100

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30000
WS_MAX_CONNECTIONS=1000

# Security
BCRYPT_ROUNDS=12
CORS_ORIGIN=http://localhost:5173
HELMET_ENABLED=true

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# Monitoring
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
