# 📊 مدير الصفقات الشامل v2.0

تطبيق متقدم لمتابعة وإدارة محفظة العملات المشفرة مع دعم كامل للغة العربية.

## ✨ الميزات الجديدة

### 🏗️ البنية المحدثة
- **Frontend**: React 18 + TypeScript + Vite
- **Backend**: Node.js + Express + TypeScript  
- **Database**: SQLite + Prisma ORM
- **State Management**: Zustand
- **Styling**: Tailwind CSS + Headless UI
- **Real-time Updates**: WebSocket

### 🚀 الميزات المتقدمة
- ✅ متابعة متعددة العملات مع DCA
- ✅ تحديث أسعار فوري من مصادر متعددة
- ✅ تحليلات تقنية متقدمة
- ✅ تنبيهات ذكية
- ✅ تصدير التقارير (PDF/Excel)
- ✅ نسخ احتياطي سحابي
- ✅ واجهة عربية متجاوبة
- ✅ وضع داكن/فاتح
- ✅ PWA (يعمل بدون إنترنت)

## 🛠️ التثبيت والتشغيل

### المتطلبات
- Node.js 18+
- npm أو yarn

### التثبيت
```bash
# تثبيت جميع التبعيات
npm run install:all

# تشغيل التطبيق في وضع التطوير
npm run dev

# بناء التطبيق للإنتاج
npm run build

# تشغيل التطبيق في الإنتاج
npm start
```

## 📁 هيكل المشروع

```
crypto-portfolio-manager/
├── frontend/                 # تطبيق React
│   ├── src/
│   │   ├── components/      # المكونات القابلة لإعادة الاستخدام
│   │   ├── pages/          # صفحات التطبيق
│   │   ├── hooks/          # React Hooks مخصصة
│   │   ├── store/          # إدارة الحالة (Zustand)
│   │   ├── services/       # خدمات API
│   │   ├── utils/          # دوال مساعدة
│   │   └── types/          # أنواع TypeScript
│   └── public/             # الملفات الثابتة
├── backend/                 # خادم Node.js
│   ├── src/
│   │   ├── routes/         # مسارات API
│   │   ├── controllers/    # منطق التحكم
│   │   ├── services/       # خدمات الأعمال
│   │   ├── models/         # نماذج البيانات
│   │   ├── middleware/     # وسطاء Express
│   │   └── utils/          # دوال مساعدة
│   └── prisma/             # قاعدة البيانات
├── shared/                  # أنواع وثوابت مشتركة
└── docs/                   # التوثيق
```

## 🔧 التطوير

### Frontend
```bash
cd frontend
npm run dev
```

### Backend
```bash
cd backend
npm run dev
```

## 📱 الاستخدام

1. افتح المتصفح على `http://localhost:5173`
2. أضف عملاتك المشفرة
3. أدخل تفاصيل الصفقات
4. راقب الأرباح والخسائر في الوقت الفعلي
5. استخدم أدوات التحليل المتقدمة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

## 📄 الترخيص

MIT License

## 👨‍💻 المطور

**المهندس معتز [تداولجي]**
- Telegram: [@TadawulGY](https://t.me/TadawulGY)

---

> نسألكم الدعاء بظهر الغيب 🤲
