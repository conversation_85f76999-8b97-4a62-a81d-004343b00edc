{"name": "crypto-portfolio-manager", "version": "2.0.0", "description": "مدير الصفقات الشامل - تطبيق متقدم لمتابعة العملات المشفرة", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "start": "cd backend && npm start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test"}, "keywords": ["crypto", "portfolio", "trading", "arabic", "react", "typescript"], "author": "المهندس معتز [تداولجي]", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "backend", "shared"]}