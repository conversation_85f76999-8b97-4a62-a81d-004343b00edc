// أنواع البيانات المشتركة بين Frontend و Backend

export interface CoinData {
  id?: string;
  symbol: string;
  name?: string;
  initialEntryPrice: number;
  initialAmountDollars: number;
  repurchases: RepurchaseEntry[];
  targets: TargetSettings;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface RepurchaseEntry {
  id?: string;
  price: number;
  amount: number;
  date?: Date;
  executed?: boolean;
}

export interface TargetSettings {
  tp1: number; // Take Profit 1 percentage
  tp2: number; // Take Profit 2 percentage  
  tp3: number; // Take Profit 3 percentage
  sl: number;  // Stop Loss percentage
}

export interface MarketPrice {
  symbol: string;
  price: number;
  source: 'binance' | 'kucoin' | 'coinbase';
  timestamp: Date;
  change24h?: number;
  volume24h?: number;
}

export interface PortfolioSummary {
  totalInvested: number;
  totalCurrentValue: number;
  totalPnlAmount: number;
  totalPnlPercent: number;
  coinsCount: number;
  lastUpdated: Date;
}

export interface CoinSummary {
  symbol: string;
  totalCoinQty: number;
  totalInvestedAmount: number;
  averageEntryPrice: number;
  marketPrice: number | null;
  currentPortfolioValue: number;
  pnlAmount: number;
  pnlPercent: number;
  error?: string;
}

export interface CalculatedTargets {
  tp1Price: number;
  tp2Price: number;
  tp3Price: number;
  slPrice: number;
}

export interface RepurchaseCalculation {
  index: number;
  downPercent: number;
  price: number;
  amount: number;
  quantity: number;
  pnlAmount: number;
  pnlPercent: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PriceAlert {
  id: string;
  symbol: string;
  type: 'above' | 'below' | 'target' | 'stop_loss';
  targetPrice: number;
  currentPrice: number;
  triggered: boolean;
  createdAt: Date;
  triggeredAt?: Date;
}

export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'ar' | 'en';
  autoRefresh: boolean;
  refreshInterval: number; // in seconds
  notifications: boolean;
  soundAlerts: boolean;
  defaultCurrency: 'USD' | 'SAR' | 'AED';
}

export interface ExportData {
  coins: CoinData[];
  summary: PortfolioSummary;
  exportDate: Date;
  version: string;
}

// WebSocket Events
export interface WebSocketMessage {
  type: 'price_update' | 'alert_triggered' | 'portfolio_update';
  data: any;
  timestamp: Date;
}

export interface PriceUpdateMessage extends WebSocketMessage {
  type: 'price_update';
  data: {
    symbol: string;
    price: number;
    change24h: number;
  };
}

export interface AlertTriggeredMessage extends WebSocketMessage {
  type: 'alert_triggered';
  data: PriceAlert;
}

// API Endpoints Types
export interface GetCoinsResponse extends ApiResponse<CoinData[]> {}
export interface GetCoinResponse extends ApiResponse<CoinData> {}
export interface CreateCoinResponse extends ApiResponse<CoinData> {}
export interface UpdateCoinResponse extends ApiResponse<CoinData> {}
export interface DeleteCoinResponse extends ApiResponse<boolean> {}
export interface GetPricesResponse extends ApiResponse<MarketPrice[]> {}
export interface GetSummaryResponse extends ApiResponse<PortfolioSummary> {}

// Form Types
export interface CoinFormData {
  symbol: string;
  initialEntryPrice: string;
  initialAmountDollars: string;
  targets: {
    tp1: string;
    tp2: string;
    tp3: string;
    sl: string;
  };
}

export interface RepurchaseFormData {
  price: string;
  amount: string;
}

// Utility Types
export type SortDirection = 'asc' | 'desc';
export type SortField = 'symbol' | 'pnlAmount' | 'pnlPercent' | 'totalInvested' | 'currentValue';

export interface SortConfig {
  field: SortField;
  direction: SortDirection;
}

export interface FilterConfig {
  showProfitable?: boolean;
  showLosing?: boolean;
  minInvestment?: number;
  maxInvestment?: number;
  symbols?: string[];
}

// Chart Data Types
export interface ChartDataPoint {
  date: string;
  value: number;
  pnl: number;
}

export interface PerformanceChart {
  daily: ChartDataPoint[];
  weekly: ChartDataPoint[];
  monthly: ChartDataPoint[];
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}
