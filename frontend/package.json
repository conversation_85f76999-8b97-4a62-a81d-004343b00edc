{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.20.1", "zustand": "^4.4.7", "axios": "^1.6.2", "recharts": "^2.8.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "@headlessui/react": "^1.7.17", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vitest": "^0.34.6"}}