import React, { useMemo } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  PieChart, 
  BarChart3,
  Target,
  AlertTriangle
} from 'lucide-react';
import { usePortfolioStore } from '../store/portfolioStore';
import { 
  PieChart as RechartsPieChart, 
  Cell, 
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend
} from 'recharts';

const Analytics: React.FC = () => {
  const { coins, marketPrices } = usePortfolioStore();

  // حساب البيانات التحليلية
  const analyticsData = useMemo(() => {
    let totalInvested = 0;
    let totalCurrentValue = 0;
    let profitableCoins = 0;
    let losingCoins = 0;
    
    const coinAnalytics = coins.map(coin => {
      const marketPrice = marketPrices[coin.symbol]?.price || 0;
      
      // حساب الاستثمار الكلي
      let coinTotalInvested = coin.initialAmountDollars;
      let totalCoinQty = coin.initialAmountDollars / coin.initialEntryPrice;

      coin.repurchases.forEach(rep => {
        if (rep.price > 0 && rep.amount > 0) {
          coinTotalInvested += rep.amount;
          totalCoinQty += rep.amount / rep.price;
        }
      });

      const averageEntryPrice = coinTotalInvested / totalCoinQty;
      const currentValue = totalCoinQty * marketPrice;
      const pnlAmount = currentValue - coinTotalInvested;
      const pnlPercent = coinTotalInvested > 0 ? (pnlAmount / coinTotalInvested) * 100 : 0;

      totalInvested += coinTotalInvested;
      totalCurrentValue += currentValue;

      if (pnlAmount > 0) profitableCoins++;
      else if (pnlAmount < 0) losingCoins++;

      return {
        symbol: coin.symbol,
        invested: coinTotalInvested,
        currentValue,
        pnlAmount,
        pnlPercent,
        averageEntryPrice,
        marketPrice,
        allocation: 0 // سيتم حسابها لاحقاً
      };
    });

    // حساب نسبة التوزيع
    coinAnalytics.forEach(coin => {
      coin.allocation = totalInvested > 0 ? (coin.invested / totalInvested) * 100 : 0;
    });

    const totalPnlAmount = totalCurrentValue - totalInvested;
    const totalPnlPercent = totalInvested > 0 ? (totalPnlAmount / totalInvested) * 100 : 0;

    return {
      totalInvested,
      totalCurrentValue,
      totalPnlAmount,
      totalPnlPercent,
      profitableCoins,
      losingCoins,
      neutralCoins: coins.length - profitableCoins - losingCoins,
      coinAnalytics: coinAnalytics.sort((a, b) => b.invested - a.invested)
    };
  }, [coins, marketPrices]);

  // بيانات الرسم البياني الدائري
  const pieChartData = analyticsData.coinAnalytics.slice(0, 8).map(coin => ({
    name: coin.symbol,
    value: coin.allocation,
    invested: coin.invested
  }));

  // بيانات الرسم البياني العمودي
  const barChartData = analyticsData.coinAnalytics.slice(0, 10).map(coin => ({
    symbol: coin.symbol,
    invested: coin.invested,
    current: coin.currentValue,
    pnl: coin.pnlAmount
  }));

  // ألوان الرسم البياني
  const COLORS = [
    '#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6',
    '#06b6d4', '#f97316', '#84cc16', '#ec4899', '#6b7280'
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercent = (percent: number) => {
    return `${percent.toFixed(2)}%`;
  };

  if (coins.length === 0) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="h-24 w-24 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          لا توجد بيانات للتحليل
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          أضف عملات إلى محفظتك لمشاهدة التحليلات المتقدمة
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          التحليلات المتقدمة
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          تحليل شامل لأداء محفظتك الاستثمارية
        </p>
      </div>

      {/* الإحصائيات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الاستثمار</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white number-font">
                {formatCurrency(analyticsData.totalInvested)}
              </p>
            </div>
            <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">القيمة الحالية</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white number-font">
                {formatCurrency(analyticsData.totalCurrentValue)}
              </p>
            </div>
            <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              <PieChart className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الربح/الخسارة</p>
              <p className={`text-2xl font-bold number-font ${
                analyticsData.totalPnlAmount >= 0 
                  ? 'text-green-600 dark:text-green-400' 
                  : 'text-red-600 dark:text-red-400'
              }`}>
                {formatCurrency(analyticsData.totalPnlAmount)}
              </p>
              <p className={`text-sm ${
                analyticsData.totalPnlAmount >= 0 
                  ? 'text-green-600 dark:text-green-400' 
                  : 'text-red-600 dark:text-red-400'
              }`}>
                {formatPercent(analyticsData.totalPnlPercent)}
              </p>
            </div>
            <div className={`h-12 w-12 rounded-lg flex items-center justify-center ${
              analyticsData.totalPnlAmount >= 0 
                ? 'bg-green-100 dark:bg-green-900' 
                : 'bg-red-100 dark:bg-red-900'
            }`}>
              {analyticsData.totalPnlAmount >= 0 ? (
                <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
              ) : (
                <TrendingDown className="h-6 w-6 text-red-600 dark:text-red-400" />
              )}
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">توزيع الأداء</p>
              <div className="flex space-x-4 space-x-reverse mt-2">
                <div className="text-center">
                  <p className="text-lg font-bold text-green-600 dark:text-green-400">
                    {analyticsData.profitableCoins}
                  </p>
                  <p className="text-xs text-gray-500">ربح</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-bold text-red-600 dark:text-red-400">
                    {analyticsData.losingCoins}
                  </p>
                  <p className="text-xs text-gray-500">خسارة</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-bold text-gray-600 dark:text-gray-400">
                    {analyticsData.neutralCoins}
                  </p>
                  <p className="text-xs text-gray-500">محايد</p>
                </div>
              </div>
            </div>
            <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
              <Target className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>
      </div>

      {/* الرسوم البيانية */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* الرسم البياني الدائري - توزيع المحفظة */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            توزيع المحفظة
          </h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart>
                <Pie
                  data={pieChartData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
                >
                  {pieChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: number, name: string, props: any) => [
                    `${value.toFixed(2)}%`,
                    `${formatCurrency(props.payload.invested)}`
                  ]}
                />
              </RechartsPieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* الرسم البياني العمودي - مقارنة الاستثمار والقيمة الحالية */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            مقارنة الاستثمار والقيمة الحالية
          </h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={barChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="symbol" />
                <YAxis />
                <Tooltip 
                  formatter={(value: number) => formatCurrency(value)}
                  labelStyle={{ color: '#374151' }}
                />
                <Legend />
                <Bar dataKey="invested" fill="#3b82f6" name="المستثمر" />
                <Bar dataKey="current" fill="#22c55e" name="القيمة الحالية" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* جدول تفصيلي للعملات */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            تحليل مفصل للعملات
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  العملة
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  المستثمر
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  القيمة الحالية
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الربح/الخسارة
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  النسبة %
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  التوزيع %
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {analyticsData.coinAnalytics.map((coin) => (
                <tr key={coin.symbol} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {coin.symbol}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className="text-sm text-gray-900 dark:text-white number-font">
                      {formatCurrency(coin.invested)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className="text-sm text-gray-900 dark:text-white number-font">
                      {formatCurrency(coin.currentValue)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className={`text-sm font-medium number-font ${
                      coin.pnlAmount >= 0 
                        ? 'text-green-600 dark:text-green-400' 
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {formatCurrency(coin.pnlAmount)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className={`text-sm font-medium number-font ${
                      coin.pnlPercent >= 0 
                        ? 'text-green-600 dark:text-green-400' 
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {formatPercent(coin.pnlPercent)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className="text-sm text-gray-900 dark:text-white number-font">
                      {formatPercent(coin.allocation)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
