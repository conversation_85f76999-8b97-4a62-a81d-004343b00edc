import React from 'react';
import { 
  <PERSON>, 
  <PERSON>, 
  RefreshCw, 
  Bell, 
  Volume2, 
  VolumeX,
  Globe,
  DollarSign,
  Trash2,
  Download,
  Upload
} from 'lucide-react';
import { usePortfolioStore } from '../store/portfolioStore';
import toast from 'react-hot-toast';

const Settings: React.FC = () => {
  const { 
    userSettings, 
    updateUserSettings, 
    toggleTheme,
    resetStore,
    coins
  } = usePortfolioStore();

  const handleAutoRefreshChange = (enabled: boolean) => {
    updateUserSettings({ autoRefresh: enabled });
    toast.success(enabled ? 'تم تفعيل التحديث التلقائي' : 'تم إيقاف التحديث التلقائي');
  };

  const handleRefreshIntervalChange = (interval: number) => {
    updateUserSettings({ refreshInterval: interval });
    toast.success(`تم تغيير فترة التحديث إلى ${interval} ثانية`);
  };

  const handleNotificationsChange = (enabled: boolean) => {
    updateUserSettings({ notifications: enabled });
    toast.success(enabled ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات');
  };

  const handleSoundAlertsChange = (enabled: boolean) => {
    updateUserSettings({ soundAlerts: enabled });
    toast.success(enabled ? 'تم تفعيل التنبيهات الصوتية' : 'تم إيقاف التنبيهات الصوتية');
  };

  const handleLanguageChange = (language: 'ar' | 'en') => {
    updateUserSettings({ language });
    toast.success(language === 'ar' ? 'تم تغيير اللغة إلى العربية' : 'Language changed to English');
  };

  const handleCurrencyChange = (currency: 'USD' | 'SAR' | 'AED') => {
    updateUserSettings({ defaultCurrency: currency });
    toast.success(`تم تغيير العملة الافتراضية إلى ${currency}`);
  };

  const handleResetData = () => {
    const confirmed = window.confirm(
      'هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.'
    );
    
    if (confirmed) {
      resetStore();
      toast.success('تم حذف جميع البيانات');
    }
  };

  const handleExportSettings = () => {
    try {
      const settingsData = {
        userSettings,
        exportDate: new Date().toISOString(),
        version: '2.0.0'
      };

      const dataStr = JSON.stringify(settingsData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `crypto-portfolio-settings-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
      toast.success('تم تصدير الإعدادات بنجاح');
    } catch (error) {
      console.error('Error exporting settings:', error);
      toast.error('حدث خطأ أثناء تصدير الإعدادات');
    }
  };

  const settingSections = [
    {
      title: 'المظهر',
      icon: userSettings.theme === 'light' ? Sun : Moon,
      settings: [
        {
          label: 'الوضع المظلم',
          description: 'تبديل بين الوضع الفاتح والمظلم',
          type: 'toggle' as const,
          value: userSettings.theme === 'dark',
          onChange: toggleTheme
        }
      ]
    },
    {
      title: 'التحديث التلقائي',
      icon: RefreshCw,
      settings: [
        {
          label: 'تفعيل التحديث التلقائي',
          description: 'تحديث أسعار العملات تلقائياً',
          type: 'toggle' as const,
          value: userSettings.autoRefresh,
          onChange: handleAutoRefreshChange
        },
        {
          label: 'فترة التحديث (ثانية)',
          description: 'المدة بين كل تحديث تلقائي',
          type: 'select' as const,
          value: userSettings.refreshInterval,
          options: [
            { value: 15, label: '15 ثانية' },
            { value: 30, label: '30 ثانية' },
            { value: 60, label: 'دقيقة واحدة' },
            { value: 300, label: '5 دقائق' },
            { value: 600, label: '10 دقائق' }
          ],
          onChange: handleRefreshIntervalChange,
          disabled: !userSettings.autoRefresh
        }
      ]
    },
    {
      title: 'الإشعارات',
      icon: Bell,
      settings: [
        {
          label: 'تفعيل الإشعارات',
          description: 'إشعارات عند تحقق الأهداف أو وقف الخسارة',
          type: 'toggle' as const,
          value: userSettings.notifications,
          onChange: handleNotificationsChange
        },
        {
          label: 'التنبيهات الصوتية',
          description: 'تشغيل أصوات عند الإشعارات المهمة',
          type: 'toggle' as const,
          value: userSettings.soundAlerts,
          onChange: handleSoundAlertsChange,
          disabled: !userSettings.notifications
        }
      ]
    },
    {
      title: 'اللغة والعملة',
      icon: Globe,
      settings: [
        {
          label: 'اللغة',
          description: 'لغة واجهة التطبيق',
          type: 'select' as const,
          value: userSettings.language,
          options: [
            { value: 'ar', label: 'العربية' },
            { value: 'en', label: 'English' }
          ],
          onChange: handleLanguageChange
        },
        {
          label: 'العملة الافتراضية',
          description: 'العملة المستخدمة في العرض',
          type: 'select' as const,
          value: userSettings.defaultCurrency,
          options: [
            { value: 'USD', label: 'دولار أمريكي (USD)' },
            { value: 'SAR', label: 'ريال سعودي (SAR)' },
            { value: 'AED', label: 'درهم إماراتي (AED)' }
          ],
          onChange: handleCurrencyChange
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          الإعدادات
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          تخصيص تجربة استخدام التطبيق حسب تفضيلاتك
        </p>
      </div>

      {/* أقسام الإعدادات */}
      <div className="space-y-6">
        {settingSections.map((section, sectionIndex) => {
          const SectionIcon = section.icon;
          
          return (
            <div
              key={sectionIndex}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
            >
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className="h-8 w-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <SectionIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                  </div>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {section.title}
                  </h2>
                </div>
              </div>

              <div className="p-6 space-y-6">
                {section.settings.map((setting, settingIndex) => (
                  <div key={settingIndex} className="flex items-center justify-between">
                    <div className="flex-1">
                      <label className="text-sm font-medium text-gray-900 dark:text-white">
                        {setting.label}
                      </label>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {setting.description}
                      </p>
                    </div>

                    <div className="mr-4">
                      {setting.type === 'toggle' && (
                        <button
                          onClick={() => setting.onChange(!setting.value)}
                          disabled={setting.disabled}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${
                            setting.value
                              ? 'bg-primary-600'
                              : 'bg-gray-200 dark:bg-gray-700'
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              setting.value ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      )}

                      {setting.type === 'select' && (
                        <select
                          value={setting.value}
                          onChange={(e) => setting.onChange(
                            setting.label.includes('فترة') 
                              ? Number(e.target.value)
                              : e.target.value
                          )}
                          disabled={setting.disabled}
                          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {setting.options?.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* إدارة البيانات */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="h-8 w-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
              <DollarSign className="h-5 w-5 text-orange-600 dark:text-orange-400" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              إدارة البيانات
            </h2>
          </div>
        </div>

        <div className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={handleExportSettings}
              className="flex items-center justify-center space-x-2 space-x-reverse px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Download className="h-5 w-5" />
              <span>تصدير الإعدادات</span>
            </button>

            <button
              onClick={handleResetData}
              disabled={coins.length === 0}
              className="flex items-center justify-center space-x-2 space-x-reverse px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Trash2 className="h-5 w-5" />
              <span>حذف جميع البيانات</span>
            </button>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 border border-yellow-200 dark:border-yellow-800">
            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-600 dark:text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-300">
                  تنبيه مهم
                </h3>
                <p className="text-sm text-yellow-700 dark:text-yellow-400 mt-1">
                  تأكد من تصدير بياناتك بانتظام كنسخة احتياطية. حذف البيانات لا يمكن التراجع عنه.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* معلومات التطبيق */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            معلومات التطبيق
          </h2>
          
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">الإصدار:</span>
              <span className="text-gray-900 dark:text-white font-medium">2.0.0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">المطور:</span>
              <span className="text-gray-900 dark:text-white font-medium">المهندس معتز [تداولجي]</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">التليغرام:</span>
              <a 
                href="https://t.me/TadawulGY" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-primary-600 dark:text-primary-400 hover:underline font-medium"
              >
                @TadawulGY
              </a>
            </div>
          </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              نسألكم الدعاء بظهر الغيب 🤲
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
