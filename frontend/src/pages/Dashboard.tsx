import React, { useEffect } from 'react';
import { usePortfolioStore } from '../store/portfolioStore';
import PortfolioSummary from '../components/dashboard/PortfolioSummary';
import CoinsTable from '../components/dashboard/CoinsTable';
import QuickActions from '../components/dashboard/QuickActions';
import RecentActivity from '../components/dashboard/RecentActivity';
import { fetchPricesDirectly } from '../services/api';

const Dashboard: React.FC = () => {
  const { 
    coins, 
    updateMarketPrices, 
    userSettings,
    isLoading,
    setLoading 
  } = usePortfolioStore();

  // تحديث الأسعار عند تحميل الصفحة
  useEffect(() => {
    const fetchInitialPrices = async () => {
      if (coins.length === 0) return;

      setLoading(true);
      try {
        const symbols = coins.map(coin => coin.symbol);
        const prices = await fetchPricesDirectly(symbols);
        if (prices.length > 0) {
          updateMarketPrices(prices);
        }
      } catch (error) {
        console.error('Error fetching initial prices:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchInitialPrices();
  }, [coins.length, updateMarketPrices, setLoading]);

  // التحديث التلقائي للأسعار
  useEffect(() => {
    if (!userSettings.autoRefresh || coins.length === 0) return;

    const interval = setInterval(async () => {
      try {
        const symbols = coins.map(coin => coin.symbol);
        const prices = await fetchPricesDirectly(symbols);
        if (prices.length > 0) {
          updateMarketPrices(prices);
        }
      } catch (error) {
        console.error('Error in auto refresh:', error);
      }
    }, userSettings.refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [
    userSettings.autoRefresh, 
    userSettings.refreshInterval, 
    coins, 
    updateMarketPrices
  ]);

  if (coins.length === 0) {
    return (
      <div className="space-y-6">
        {/* رسالة ترحيب للمستخدمين الجدد */}
        <div className="text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="mb-6">
              <div className="mx-auto h-24 w-24 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                <svg
                  className="h-12 w-12 text-primary-600 dark:text-primary-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
              </div>
            </div>
            
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              مرحباً بك في مدير الصفقات الشامل
            </h2>
            
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              ابدأ بإضافة عملاتك المشفرة لمتابعة أداء محفظتك وإدارة صفقاتك بشكل احترافي
            </p>
            
            <QuickActions />
          </div>
        </div>

        {/* ميزات التطبيق */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center mb-4">
              <div className="h-10 w-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                <svg
                  className="h-6 w-6 text-green-600 dark:text-green-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <h3 className="mr-3 text-lg font-semibold text-gray-900 dark:text-white">
                متابعة شاملة
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              راقب جميع عملاتك المشفرة في مكان واحد مع تحديث الأسعار في الوقت الفعلي
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center mb-4">
              <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <svg
                  className="h-6 w-6 text-blue-600 dark:text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                  />
                </svg>
              </div>
              <h3 className="mr-3 text-lg font-semibold text-gray-900 dark:text-white">
                استراتيجية DCA
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              إدارة متقدمة لاستراتيجية متوسط التكلفة بالدولار مع حساب الأهداف والمخاطر
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center mb-4">
              <div className="h-10 w-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                <svg
                  className="h-6 w-6 text-purple-600 dark:text-purple-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="mr-3 text-lg font-semibold text-gray-900 dark:text-white">
                تحليلات متقدمة
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              تقارير مفصلة وتحليلات عميقة لأداء محفظتك مع رسوم بيانية تفاعلية
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* ملخص المحفظة */}
      <PortfolioSummary />

      {/* الإجراءات السريعة */}
      <QuickActions />

      {/* جدول العملات */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              العملات المراقبة
            </h2>
            <div className="flex items-center space-x-2 space-x-reverse">
              {isLoading && (
                <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
                  <span>جاري التحديث...</span>
                </div>
              )}
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {coins.length} عملة
              </span>
            </div>
          </div>
        </div>
        
        <CoinsTable />
      </div>

      {/* النشاط الأخير */}
      <RecentActivity />
    </div>
  );
};

export default Dashboard;
