import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Edit, 
  Save, 
  X, 
  TrendingUp, 
  TrendingDown,
  Target,
  AlertTriangle,
  Plus,
  Trash2
} from 'lucide-react';
import { usePortfolioStore } from '../store/portfolioStore';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

const CoinDetails: React.FC = () => {
  const { symbol } = useParams<{ symbol: string }>();
  const navigate = useNavigate();
  const { coins, marketPrices, updateCoin, deleteCoin } = usePortfolioStore();
  const [isEditing, setIsEditing] = useState(false);

  const coin = coins.find(c => c.symbol === symbol);
  const marketPrice = marketPrices[symbol || '']?.price || 0;

  const { register, handleSubmit, reset, watch } = useForm();

  useEffect(() => {
    if (coin) {
      reset({
        initialEntryPrice: coin.initialEntryPrice,
        initialAmountDollars: coin.initialAmountDollars,
        tp1: coin.targets.tp1,
        tp2: coin.targets.tp2,
        tp3: coin.targets.tp3,
        sl: coin.targets.sl,
        ...coin.repurchases.reduce((acc, rep, index) => {
          acc[`repurchase_${index}_price`] = rep.price;
          acc[`repurchase_${index}_amount`] = rep.amount;
          return acc;
        }, {} as any)
      });
    }
  }, [coin, reset]);

  if (!coin || !symbol) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          العملة غير موجودة
        </h2>
        <button
          onClick={() => navigate('/')}
          className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
        >
          العودة للرئيسية
        </button>
      </div>
    );
  }

  // حساب البيانات
  let totalInvested = coin.initialAmountDollars;
  let totalQty = coin.initialAmountDollars / coin.initialEntryPrice;

  coin.repurchases.forEach(rep => {
    if (rep.price > 0 && rep.amount > 0) {
      totalInvested += rep.amount;
      totalQty += rep.amount / rep.price;
    }
  });

  const averageEntryPrice = totalInvested / totalQty;
  const currentValue = totalQty * marketPrice;
  const pnlAmount = currentValue - totalInvested;
  const pnlPercent = totalInvested > 0 ? (pnlAmount / totalInvested) * 100 : 0;

  // حساب الأهداف
  const targets = {
    tp1: averageEntryPrice * (1 + coin.targets.tp1 / 100),
    tp2: averageEntryPrice * (1 + coin.targets.tp2 / 100),
    tp3: averageEntryPrice * (1 + coin.targets.tp3 / 100),
    sl: averageEntryPrice * (1 - coin.targets.sl / 100)
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 8,
    }).format(amount);
  };

  const formatNumber = (num: number, decimals: number = 8) => {
    return new Intl.NumberFormat('ar-SA', {
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals,
    }).format(num);
  };

  const onSubmit = (data: any) => {
    try {
      const updatedCoin = {
        ...coin,
        initialEntryPrice: Number(data.initialEntryPrice),
        initialAmountDollars: Number(data.initialAmountDollars),
        targets: {
          tp1: Number(data.tp1) || 0,
          tp2: Number(data.tp2) || 0,
          tp3: Number(data.tp3) || 0,
          sl: Number(data.sl) || 0
        },
        repurchases: Array.from({ length: 10 }, (_, index) => ({
          price: Number(data[`repurchase_${index}_price`]) || 0,
          amount: Number(data[`repurchase_${index}_amount`]) || 0
        }))
      };

      updateCoin(symbol, updatedCoin);
      setIsEditing(false);
      toast.success('تم تحديث بيانات العملة بنجاح');
    } catch (error) {
      toast.error('حدث خطأ أثناء التحديث');
    }
  };

  const handleDelete = () => {
    const confirmed = window.confirm(`هل أنت متأكد من حذف ${symbol}؟`);
    if (confirmed) {
      deleteCoin(symbol);
      toast.success(`تم حذف ${symbol} بنجاح`);
      navigate('/');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 space-x-reverse">
          <button
            onClick={() => navigate('/')}
            className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              تفاصيل {symbol}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              إدارة وتحليل صفقة {symbol}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2 space-x-reverse">
          {isEditing ? (
            <>
              <button
                onClick={handleSubmit(onSubmit)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2 space-x-reverse"
              >
                <Save className="h-4 w-4" />
                <span>حفظ</span>
              </button>
              <button
                onClick={() => {
                  setIsEditing(false);
                  reset();
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center space-x-2 space-x-reverse"
              >
                <X className="h-4 w-4" />
                <span>إلغاء</span>
              </button>
            </>
          ) : (
            <>
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 flex items-center space-x-2 space-x-reverse"
              >
                <Edit className="h-4 w-4" />
                <span>تعديل</span>
              </button>
              <button
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center space-x-2 space-x-reverse"
              >
                <Trash2 className="h-4 w-4" />
                <span>حذف</span>
              </button>
            </>
          )}
        </div>
      </div>

      {/* الملخص السريع */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الكمية</p>
              <p className="text-xl font-bold text-gray-900 dark:text-white number-font">
                {formatNumber(totalQty)}
              </p>
            </div>
            <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <span className="text-sm font-bold text-blue-600 dark:text-blue-400">
                {symbol.slice(0, 2)}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">متوسط الدخول</p>
              <p className="text-xl font-bold text-gray-900 dark:text-white number-font">
                {formatCurrency(averageEntryPrice)}
              </p>
            </div>
            <div className="h-10 w-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
              <Target className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">السعر الحالي</p>
              <p className="text-xl font-bold text-gray-900 dark:text-white number-font">
                {marketPrice > 0 ? formatCurrency(marketPrice) : 'غير متوفر'}
              </p>
            </div>
            <div className="h-10 w-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">الربح/الخسارة</p>
              <p className={`text-xl font-bold number-font ${
                pnlAmount >= 0 
                  ? 'text-green-600 dark:text-green-400' 
                  : 'text-red-600 dark:text-red-400'
              }`}>
                {formatCurrency(pnlAmount)}
              </p>
              <p className={`text-sm number-font ${
                pnlAmount >= 0 
                  ? 'text-green-600 dark:text-green-400' 
                  : 'text-red-600 dark:text-red-400'
              }`}>
                {pnlPercent.toFixed(2)}%
              </p>
            </div>
            <div className={`h-10 w-10 rounded-lg flex items-center justify-center ${
              pnlAmount >= 0 
                ? 'bg-green-100 dark:bg-green-900' 
                : 'bg-red-100 dark:bg-red-900'
            }`}>
              {pnlAmount >= 0 ? (
                <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
              ) : (
                <TrendingDown className="h-5 w-5 text-red-600 dark:text-red-400" />
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* بيانات الدخول الأولي */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            بيانات الدخول الأولي
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                سعر الدخول الأولي
              </label>
              {isEditing ? (
                <input
                  type="number"
                  step="any"
                  {...register('initialEntryPrice')}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white number-font"
                />
              ) : (
                <div className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-lg text-gray-900 dark:text-white number-font">
                  {formatCurrency(coin.initialEntryPrice)}
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                المبلغ المستثمر
              </label>
              {isEditing ? (
                <input
                  type="number"
                  step="any"
                  {...register('initialAmountDollars')}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white number-font"
                />
              ) : (
                <div className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-lg text-gray-900 dark:text-white number-font">
                  {formatCurrency(coin.initialAmountDollars)}
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                الكمية المحصلة
              </label>
              <div className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-lg text-gray-900 dark:text-white number-font">
                {formatNumber(coin.initialAmountDollars / coin.initialEntryPrice)}
              </div>
            </div>
          </div>
        </div>

        {/* الأهداف ووقف الخسارة */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            الأهداف ووقف الخسارة
          </h3>
          
          <div className="space-y-4">
            {['tp1', 'tp2', 'tp3'].map((target, index) => (
              <div key={target} className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    هدف {index + 1} (%)
                  </label>
                  {isEditing ? (
                    <input
                      type="number"
                      step="any"
                      {...register(target)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white number-font"
                    />
                  ) : (
                    <div className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-lg text-gray-900 dark:text-white number-font">
                      {coin.targets[target as keyof typeof coin.targets]}%
                    </div>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    سعر الهدف
                  </label>
                  <div className="px-3 py-2 bg-green-50 dark:bg-green-900/20 rounded-lg text-green-700 dark:text-green-400 number-font">
                    {formatCurrency(targets[target as keyof typeof targets])}
                  </div>
                </div>
              </div>
            ))}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  وقف الخسارة (%)
                </label>
                {isEditing ? (
                  <input
                    type="number"
                    step="any"
                    {...register('sl')}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white number-font"
                  />
                ) : (
                  <div className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-lg text-gray-900 dark:text-white number-font">
                    {coin.targets.sl}%
                  </div>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  سعر وقف الخسارة
                </label>
                <div className="px-3 py-2 bg-red-50 dark:bg-red-900/20 rounded-lg text-red-700 dark:text-red-400 number-font">
                  {formatCurrency(targets.sl)}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* جدول التعزيزات */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            تعزيزات DCA
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">#</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">السعر</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">المبلغ</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">الكمية</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">هبوط %</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {coin.repurchases.map((rep, index) => {
                const quantity = rep.price > 0 ? rep.amount / rep.price : 0;
                const downPercent = rep.price > 0 ? ((coin.initialEntryPrice - rep.price) / coin.initialEntryPrice) * 100 : 0;
                
                return (
                  <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <td className="px-6 py-4 text-center text-sm text-gray-900 dark:text-white">
                      {index + 1}
                    </td>
                    <td className="px-6 py-4 text-center">
                      {isEditing ? (
                        <input
                          type="number"
                          step="any"
                          {...register(`repurchase_${index}_price`)}
                          className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white number-font text-center"
                        />
                      ) : (
                        <span className="text-sm text-gray-900 dark:text-white number-font">
                          {rep.price > 0 ? formatCurrency(rep.price) : '-'}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 text-center">
                      {isEditing ? (
                        <input
                          type="number"
                          step="any"
                          {...register(`repurchase_${index}_amount`)}
                          className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white number-font text-center"
                        />
                      ) : (
                        <span className="text-sm text-gray-900 dark:text-white number-font">
                          {rep.amount > 0 ? formatCurrency(rep.amount) : '-'}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 text-center">
                      <span className="text-sm text-gray-900 dark:text-white number-font">
                        {quantity > 0 ? formatNumber(quantity) : '-'}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-center">
                      <span className={`text-sm font-medium number-font ${
                        downPercent > 0 ? 'text-red-600 dark:text-red-400' : 'text-gray-500'
                      }`}>
                        {downPercent > 0 ? `-${downPercent.toFixed(2)}%` : '-'}
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default CoinDetails;
