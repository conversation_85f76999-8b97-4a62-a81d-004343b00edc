import React, { useState } from 'react';
import { 
  Plus, 
  RefreshCw, 
  Download, 
  Upload,
  BarChart3,
  Settings
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { usePortfolioStore } from '../../store/portfolioStore';
import { fetchPricesDirectly } from '../../services/api';
import AddCoinModal from '../modals/AddCoinModal';
import ImportDataModal from '../modals/ImportDataModal';
import toast from 'react-hot-toast';

const QuickActions: React.FC = () => {
  const { 
    coins, 
    updateMarketPrices, 
    setLoading, 
    isLoading 
  } = usePortfolioStore();
  
  const [showAddCoinModal, setShowAddCoinModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);

  const handleRefreshPrices = async () => {
    if (coins.length === 0) {
      toast.error('لا توجد عملات لتحديث أسعارها');
      return;
    }

    setLoading(true);
    const loadingToast = toast.loading('جاري تحديث الأسعار...');

    try {
      const symbols = coins.map(coin => coin.symbol);
      const prices = await fetchPricesDirectly(symbols);
      
      if (prices.length > 0) {
        updateMarketPrices(prices);
        toast.success(`تم تحديث ${prices.length} من ${symbols.length} عملة`, {
          id: loadingToast,
        });
      } else {
        toast.error('فشل في تحديث الأسعار', { id: loadingToast });
      }
    } catch (error) {
      console.error('Error refreshing prices:', error);
      toast.error('حدث خطأ أثناء تحديث الأسعار', { id: loadingToast });
    } finally {
      setLoading(false);
    }
  };

  const handleExportData = () => {
    if (coins.length === 0) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      const dataToExport = {
        coins,
        exportDate: new Date().toISOString(),
        version: '2.0.0'
      };

      const dataStr = JSON.stringify(dataToExport, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `crypto-portfolio-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
      toast.success('تم تصدير البيانات بنجاح');
    } catch (error) {
      console.error('Error exporting data:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  const actions = [
    {
      name: 'إضافة عملة',
      description: 'أضف عملة جديدة لمتابعتها',
      icon: Plus,
      color: 'bg-green-500 hover:bg-green-600',
      onClick: () => setShowAddCoinModal(true),
      disabled: false
    },
    {
      name: 'تحديث الأسعار',
      description: 'تحديث أسعار جميع العملات',
      icon: RefreshCw,
      color: 'bg-blue-500 hover:bg-blue-600',
      onClick: handleRefreshPrices,
      disabled: coins.length === 0 || isLoading,
      loading: isLoading
    },
    {
      name: 'تصدير البيانات',
      description: 'تصدير بيانات المحفظة',
      icon: Download,
      color: 'bg-purple-500 hover:bg-purple-600',
      onClick: handleExportData,
      disabled: coins.length === 0
    },
    {
      name: 'استيراد البيانات',
      description: 'استيراد بيانات من ملف',
      icon: Upload,
      color: 'bg-orange-500 hover:bg-orange-600',
      onClick: () => setShowImportModal(true),
      disabled: false
    }
  ];

  const navigationActions = [
    {
      name: 'التحليلات',
      description: 'عرض التحليلات المتقدمة',
      icon: BarChart3,
      href: '/analytics',
      color: 'bg-indigo-500 hover:bg-indigo-600',
      disabled: coins.length === 0
    },
    {
      name: 'الإعدادات',
      description: 'إعدادات التطبيق',
      icon: Settings,
      href: '/settings',
      color: 'bg-gray-500 hover:bg-gray-600',
      disabled: false
    }
  ];

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          الإجراءات السريعة
        </h2>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {/* أزرار الإجراءات */}
          {actions.map((action, index) => {
            const Icon = action.icon;
            
            return (
              <button
                key={index}
                onClick={action.onClick}
                disabled={action.disabled}
                className={`${action.color} text-white p-4 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex flex-col items-center space-y-2`}
                title={action.description}
              >
                <div className="relative">
                  <Icon className={`h-6 w-6 ${action.loading ? 'animate-spin' : ''}`} />
                </div>
                <span className="text-sm font-medium text-center">
                  {action.name}
                </span>
              </button>
            );
          })}

          {/* روابط التنقل */}
          {navigationActions.map((action, index) => {
            const Icon = action.icon;
            
            if (action.disabled) {
              return (
                <div
                  key={`nav-${index}`}
                  className="bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 p-4 rounded-lg cursor-not-allowed flex flex-col items-center space-y-2"
                  title={action.description}
                >
                  <Icon className="h-6 w-6" />
                  <span className="text-sm font-medium text-center">
                    {action.name}
                  </span>
                </div>
              );
            }
            
            return (
              <Link
                key={`nav-${index}`}
                to={action.href!}
                className={`${action.color} text-white p-4 rounded-lg transition-all duration-200 transform hover:scale-105 flex flex-col items-center space-y-2`}
                title={action.description}
              >
                <Icon className="h-6 w-6" />
                <span className="text-sm font-medium text-center">
                  {action.name}
                </span>
              </Link>
            );
          })}
        </div>

        {/* نصائح سريعة */}
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h3 className="text-sm font-semibold text-blue-900 dark:text-blue-300 mb-2">
            💡 نصائح سريعة
          </h3>
          <ul className="text-sm text-blue-800 dark:text-blue-400 space-y-1">
            <li>• استخدم التحديث التلقائي للأسعار من الإعدادات</li>
            <li>• قم بتصدير بياناتك بانتظام كنسخة احتياطية</li>
            <li>• راجع التحليلات لفهم أداء محفظتك بشكل أفضل</li>
          </ul>
        </div>
      </div>

      {/* Modals */}
      <AddCoinModal
        isOpen={showAddCoinModal}
        onClose={() => setShowAddCoinModal(false)}
      />
      
      <ImportDataModal
        isOpen={showImportModal}
        onClose={() => setShowImportModal(false)}
      />
    </>
  );
};

export default QuickActions;
