import React from 'react';
import { 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  Plus,
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import { usePortfolioStore } from '../../store/portfolioStore';

interface ActivityItem {
  id: string;
  type: 'coin_added' | 'price_update' | 'profit' | 'loss' | 'alert';
  title: string;
  description: string;
  timestamp: Date;
  icon: React.ComponentType<any>;
  color: string;
  value?: string;
}

const RecentActivity: React.FC = () => {
  const { coins, marketPrices, lastUpdated } = usePortfolioStore();

  // إنشاء قائمة النشاطات الأخيرة
  const activities: ActivityItem[] = React.useMemo(() => {
    const items: ActivityItem[] = [];

    // إضافة نشاط تحديث الأسعار
    if (lastUpdated) {
      items.push({
        id: 'price_update',
        type: 'price_update',
        title: 'تحديث الأسعار',
        description: `تم تحديث أسعار ${Object.keys(marketPrices).length} عملة`,
        timestamp: lastUpdated,
        icon: RefreshCw,
        color: 'text-blue-600 dark:text-blue-400'
      });
    }

    // إضافة نشاطات العملات المضافة حديثاً
    coins
      .filter(coin => coin.createdAt)
      .sort((a, b) => new Date(b.createdAt!).getTime() - new Date(a.createdAt!).getTime())
      .slice(0, 3)
      .forEach(coin => {
        items.push({
          id: `coin_added_${coin.symbol}`,
          type: 'coin_added',
          title: 'إضافة عملة جديدة',
          description: `تمت إضافة ${coin.symbol} للمتابعة`,
          timestamp: new Date(coin.createdAt!),
          icon: Plus,
          color: 'text-green-600 dark:text-green-400'
        });
      });

    // إضافة نشاطات الأرباح والخسائر
    coins.forEach(coin => {
      const marketPrice = marketPrices[coin.symbol]?.price || 0;
      if (marketPrice > 0) {
        let totalInvested = coin.initialAmountDollars;
        let totalQty = coin.initialAmountDollars / coin.initialEntryPrice;

        coin.repurchases.forEach(rep => {
          if (rep.price > 0 && rep.amount > 0) {
            totalInvested += rep.amount;
            totalQty += rep.amount / rep.price;
          }
        });

        const currentValue = totalQty * marketPrice;
        const pnl = currentValue - totalInvested;
        const pnlPercent = (pnl / totalInvested) * 100;

        if (Math.abs(pnlPercent) > 5) { // إظهار التغييرات الكبيرة فقط
          items.push({
            id: `pnl_${coin.symbol}`,
            type: pnl >= 0 ? 'profit' : 'loss',
            title: pnl >= 0 ? 'ربح محقق' : 'خسارة مسجلة',
            description: `${coin.symbol}: ${pnlPercent.toFixed(2)}%`,
            timestamp: marketPrices[coin.symbol]?.timestamp || new Date(),
            icon: pnl >= 0 ? TrendingUp : TrendingDown,
            color: pnl >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400',
            value: `${pnl >= 0 ? '+' : ''}${pnl.toFixed(2)} $`
          });
        }
      }
    });

    // ترتيب حسب الوقت
    return items
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 10); // أحدث 10 نشاطات
  }, [coins, marketPrices, lastUpdated]);

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'منذ لحظات';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `منذ ${minutes} دقيقة`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `منذ ${hours} ساعة`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `منذ ${days} يوم`;
    }
  };

  const getActivityIcon = (activity: ActivityItem) => {
    const Icon = activity.icon;
    return <Icon className={`h-5 w-5 ${activity.color}`} />;
  };

  if (activities.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          النشاط الأخير
        </h2>
        <div className="text-center py-8">
          <Clock className="h-12 w-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">
            لا يوجد نشاط حديث
          </p>
          <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">
            ابدأ بإضافة عملات لمشاهدة النشاطات
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          النشاط الأخير
        </h2>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {activities.length} نشاط
        </span>
      </div>

      <div className="space-y-4">
        {activities.map((activity) => (
          <div
            key={activity.id}
            className="flex items-start space-x-4 space-x-reverse p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150"
          >
            {/* أيقونة النشاط */}
            <div className="flex-shrink-0 mt-1">
              <div className="h-8 w-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                {getActivityIcon(activity)}
              </div>
            </div>

            {/* محتوى النشاط */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {activity.title}
                </p>
                <span className="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0">
                  {formatTimeAgo(activity.timestamp)}
                </span>
              </div>
              
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {activity.description}
              </p>
              
              {activity.value && (
                <p className={`text-sm font-semibold mt-1 number-font ${activity.color}`}>
                  {activity.value}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* رابط عرض المزيد */}
      <div className="mt-6 text-center">
        <button className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">
          عرض جميع النشاطات
        </button>
      </div>
    </div>
  );
};

export default RecentActivity;
