import React, { useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  ArrowUpDown, 
  ArrowUp, 
  ArrowDown, 
  ExternalLink,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { usePortfolioStore } from '../../store/portfolioStore';
import type { CoinSummary, SortField, SortDirection } from '../../../../shared/types';

const CoinsTable: React.FC = () => {
  const { coins, marketPrices, setActiveCoin } = usePortfolioStore();
  const [sortField, setSortField] = useState<SortField>('symbol');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  // حساب بيانات العملات
  const coinsData = useMemo(() => {
    const data: CoinSummary[] = coins.map(coin => {
      const marketPrice = marketPrices[coin.symbol]?.price || 0;
      
      // حساب الاستثمار الأولي
      let totalInvestedAmount = coin.initialAmountDollars;
      let totalCoinQty = coin.initialAmountDollars / coin.initialEntryPrice;

      // إضافة التعزيزات
      coin.repurchases.forEach(repurchase => {
        if (repurchase.price > 0 && repurchase.amount > 0) {
          totalInvestedAmount += repurchase.amount;
          totalCoinQty += repurchase.amount / repurchase.price;
        }
      });

      // حساب متوسط سعر الدخول
      const averageEntryPrice = totalInvestedAmount / totalCoinQty;
      
      // حساب القيمة الحالية
      const currentPortfolioValue = totalCoinQty * marketPrice;
      
      // حساب الربح/الخسارة
      const pnlAmount = currentPortfolioValue - totalInvestedAmount;
      const pnlPercent = totalInvestedAmount > 0 ? (pnlAmount / totalInvestedAmount) * 100 : 0;

      return {
        symbol: coin.symbol,
        totalCoinQty,
        totalInvestedAmount,
        averageEntryPrice,
        marketPrice,
        currentPortfolioValue,
        pnlAmount,
        pnlPercent,
        error: marketPrice === 0 ? 'لا يوجد سعر' : undefined
      };
    });

    // ترتيب البيانات
    return data.sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];

      if (sortField === 'symbol') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }, [coins, marketPrices, sortField, sortDirection]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatNumber = (num: number, decimals: number = 8) => {
    return new Intl.NumberFormat('ar-SA', {
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals,
    }).format(num);
  };

  const formatPercent = (percent: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(percent / 100);
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4" />;
    }
    return sortDirection === 'asc' ? 
      <ArrowUp className="h-4 w-4" /> : 
      <ArrowDown className="h-4 w-4" />;
  };

  const handleRowClick = (symbol: string) => {
    setActiveCoin(symbol);
  };

  if (coinsData.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          لا توجد عملات مضافة بعد
        </p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b border-gray-200 dark:border-gray-700">
            <th className="text-right py-3 px-4">
              <button
                onClick={() => handleSort('symbol')}
                className="flex items-center space-x-1 space-x-reverse text-sm font-semibold text-gray-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400"
              >
                <span>العملة</span>
                {getSortIcon('symbol')}
              </button>
            </th>
            <th className="text-center py-3 px-4">
              <span className="text-sm font-semibold text-gray-900 dark:text-white">
                الكمية
              </span>
            </th>
            <th className="text-center py-3 px-4">
              <button
                onClick={() => handleSort('totalInvested')}
                className="flex items-center justify-center space-x-1 space-x-reverse text-sm font-semibold text-gray-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400"
              >
                <span>المستثمر</span>
                {getSortIcon('totalInvested')}
              </button>
            </th>
            <th className="text-center py-3 px-4">
              <span className="text-sm font-semibold text-gray-900 dark:text-white">
                متوسط الدخول
              </span>
            </th>
            <th className="text-center py-3 px-4">
              <span className="text-sm font-semibold text-gray-900 dark:text-white">
                السعر الحالي
              </span>
            </th>
            <th className="text-center py-3 px-4">
              <span className="text-sm font-semibold text-gray-900 dark:text-white">
                القيمة الحالية
              </span>
            </th>
            <th className="text-center py-3 px-4">
              <button
                onClick={() => handleSort('pnlAmount')}
                className="flex items-center justify-center space-x-1 space-x-reverse text-sm font-semibold text-gray-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400"
              >
                <span>الربح/الخسارة</span>
                {getSortIcon('pnlAmount')}
              </button>
            </th>
            <th className="text-center py-3 px-4">
              <span className="text-sm font-semibold text-gray-900 dark:text-white">
                الإجراءات
              </span>
            </th>
          </tr>
        </thead>
        <tbody>
          {coinsData.map((coin) => (
            <tr
              key={coin.symbol}
              className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer transition-colors duration-150"
              onClick={() => handleRowClick(coin.symbol)}
            >
              <td className="py-4 px-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className="h-8 w-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                    <span className="text-xs font-semibold text-primary-600 dark:text-primary-400">
                      {coin.symbol.slice(0, 2)}
                    </span>
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 dark:text-white">
                      {coin.symbol}
                    </div>
                  </div>
                </div>
              </td>
              
              <td className="py-4 px-4 text-center">
                <span className="text-sm text-gray-900 dark:text-white number-font">
                  {formatNumber(coin.totalCoinQty)}
                </span>
              </td>
              
              <td className="py-4 px-4 text-center">
                <span className="text-sm font-semibold text-gray-900 dark:text-white number-font">
                  {formatCurrency(coin.totalInvestedAmount)}
                </span>
              </td>
              
              <td className="py-4 px-4 text-center">
                <span className="text-sm text-gray-900 dark:text-white number-font">
                  {formatCurrency(coin.averageEntryPrice)}
                </span>
              </td>
              
              <td className="py-4 px-4 text-center">
                {coin.error ? (
                  <span className="text-sm text-red-500 dark:text-red-400">
                    {coin.error}
                  </span>
                ) : (
                  <span className="text-sm text-gray-900 dark:text-white number-font">
                    {formatCurrency(coin.marketPrice || 0)}
                  </span>
                )}
              </td>
              
              <td className="py-4 px-4 text-center">
                <span className="text-sm font-semibold text-gray-900 dark:text-white number-font">
                  {formatCurrency(coin.currentPortfolioValue)}
                </span>
              </td>
              
              <td className="py-4 px-4 text-center">
                <div className="flex flex-col items-center">
                  <div className={`flex items-center space-x-1 space-x-reverse text-sm font-semibold number-font ${
                    coin.pnlAmount >= 0 
                      ? 'text-green-600 dark:text-green-400' 
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    {coin.pnlAmount >= 0 ? (
                      <TrendingUp className="h-4 w-4" />
                    ) : (
                      <TrendingDown className="h-4 w-4" />
                    )}
                    <span>{formatCurrency(coin.pnlAmount)}</span>
                  </div>
                  <div className={`text-xs number-font ${
                    coin.pnlAmount >= 0 
                      ? 'text-green-600 dark:text-green-400' 
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    {formatPercent(coin.pnlPercent)}
                  </div>
                </div>
              </td>
              
              <td className="py-4 px-4 text-center">
                <Link
                  to={`/coin/${coin.symbol}`}
                  className="inline-flex items-center space-x-1 space-x-reverse text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm"
                  onClick={(e) => e.stopPropagation()}
                >
                  <ExternalLink className="h-4 w-4" />
                  <span>تفاصيل</span>
                </Link>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default CoinsTable;
