import React, { useMemo } from 'react';
import { TrendingUp, TrendingDown, DollarSign, Coins } from 'lucide-react';
import { usePortfolioStore } from '../../store/portfolioStore';
import type { CoinSummary } from '../../../../shared/types';

const PortfolioSummary: React.FC = () => {
  const { coins, marketPrices } = usePortfolioStore();

  // حساب ملخص المحفظة
  const portfolioSummary = useMemo(() => {
    let totalInvested = 0;
    let totalCurrentValue = 0;
    let totalPnlAmount = 0;

    const coinsSummary: CoinSummary[] = coins.map(coin => {
      const marketPrice = marketPrices[coin.symbol]?.price || 0;
      
      // حساب الاستثمار الأولي
      let coinTotalInvested = coin.initialAmountDollars;
      let totalCoinQty = coin.initialAmountDollars / coin.initialEntryPrice;

      // إضافة التعزيزات
      coin.repurchases.forEach(repurchase => {
        if (repurchase.price > 0 && repurchase.amount > 0) {
          coinTotalInvested += repurchase.amount;
          totalCoinQty += repurchase.amount / repurchase.price;
        }
      });

      // حساب متوسط سعر الدخول
      const averageEntryPrice = coinTotalInvested / totalCoinQty;
      
      // حساب القيمة الحالية
      const currentPortfolioValue = totalCoinQty * marketPrice;
      
      // حساب الربح/الخسارة
      const pnlAmount = currentPortfolioValue - coinTotalInvested;
      const pnlPercent = coinTotalInvested > 0 ? (pnlAmount / coinTotalInvested) * 100 : 0;

      totalInvested += coinTotalInvested;
      totalCurrentValue += currentPortfolioValue;
      totalPnlAmount += pnlAmount;

      return {
        symbol: coin.symbol,
        totalCoinQty,
        totalInvestedAmount: coinTotalInvested,
        averageEntryPrice,
        marketPrice,
        currentPortfolioValue,
        pnlAmount,
        pnlPercent
      };
    });

    const totalPnlPercent = totalInvested > 0 ? (totalPnlAmount / totalInvested) * 100 : 0;

    return {
      totalInvested,
      totalCurrentValue,
      totalPnlAmount,
      totalPnlPercent,
      coinsCount: coins.length,
      coinsSummary
    };
  }, [coins, marketPrices]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatPercent = (percent: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(percent / 100);
  };

  const summaryCards = [
    {
      title: 'إجمالي المبلغ المستثمر',
      value: formatCurrency(portfolioSummary.totalInvested),
      icon: DollarSign,
      color: 'blue',
      description: 'المبلغ الكلي المستثمر في جميع العملات'
    },
    {
      title: 'القيمة الحالية',
      value: formatCurrency(portfolioSummary.totalCurrentValue),
      icon: TrendingUp,
      color: 'green',
      description: 'القيمة الحالية للمحفظة بأسعار السوق'
    },
    {
      title: 'الربح/الخسارة',
      value: formatCurrency(portfolioSummary.totalPnlAmount),
      icon: portfolioSummary.totalPnlAmount >= 0 ? TrendingUp : TrendingDown,
      color: portfolioSummary.totalPnlAmount >= 0 ? 'green' : 'red',
      description: `${formatPercent(portfolioSummary.totalPnlPercent)} من إجمالي الاستثمار`,
      isProfit: portfolioSummary.totalPnlAmount >= 0
    },
    {
      title: 'عدد العملات',
      value: portfolioSummary.coinsCount.toString(),
      icon: Coins,
      color: 'purple',
      description: 'العملات المراقبة في المحفظة'
    }
  ];

  const getColorClasses = (color: string, isProfit?: boolean) => {
    if (color === 'green') {
      return {
        bg: 'bg-green-50 dark:bg-green-900/20',
        icon: 'bg-green-100 dark:bg-green-900/40 text-green-600 dark:text-green-400',
        text: 'text-green-600 dark:text-green-400'
      };
    } else if (color === 'red') {
      return {
        bg: 'bg-red-50 dark:bg-red-900/20',
        icon: 'bg-red-100 dark:bg-red-900/40 text-red-600 dark:text-red-400',
        text: 'text-red-600 dark:text-red-400'
      };
    } else if (color === 'blue') {
      return {
        bg: 'bg-blue-50 dark:bg-blue-900/20',
        icon: 'bg-blue-100 dark:bg-blue-900/40 text-blue-600 dark:text-blue-400',
        text: 'text-blue-600 dark:text-blue-400'
      };
    } else if (color === 'purple') {
      return {
        bg: 'bg-purple-50 dark:bg-purple-900/20',
        icon: 'bg-purple-100 dark:bg-purple-900/40 text-purple-600 dark:text-purple-400',
        text: 'text-purple-600 dark:text-purple-400'
      };
    }
    
    return {
      bg: 'bg-gray-50 dark:bg-gray-900/20',
      icon: 'bg-gray-100 dark:bg-gray-900/40 text-gray-600 dark:text-gray-400',
      text: 'text-gray-600 dark:text-gray-400'
    };
  };

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          ملخص المحفظة
        </h1>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}
        </div>
      </div>

      {/* البطاقات الإحصائية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {summaryCards.map((card, index) => {
          const colorClasses = getColorClasses(card.color, card.isProfit);
          const Icon = card.icon;
          
          return (
            <div
              key={index}
              className={`${colorClasses.bg} rounded-lg p-6 border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md`}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                    {card.title}
                  </p>
                  <p className={`text-2xl font-bold number-font ${
                    card.isProfit !== undefined 
                      ? card.isProfit 
                        ? 'text-green-600 dark:text-green-400' 
                        : 'text-red-600 dark:text-red-400'
                      : 'text-gray-900 dark:text-white'
                  }`}>
                    {card.value}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {card.description}
                  </p>
                </div>
                <div className={`${colorClasses.icon} p-3 rounded-lg`}>
                  <Icon className="h-6 w-6" />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* مؤشر الأداء */}
      {portfolioSummary.totalInvested > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            مؤشر الأداء
          </h3>
          
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              نسبة الربح/الخسارة
            </span>
            <span className={`text-sm font-semibold ${
              portfolioSummary.totalPnlPercent >= 0 
                ? 'text-green-600 dark:text-green-400' 
                : 'text-red-600 dark:text-red-400'
            }`}>
              {formatPercent(portfolioSummary.totalPnlPercent)}
            </span>
          </div>
          
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-500 ${
                portfolioSummary.totalPnlPercent >= 0 
                  ? 'bg-green-500' 
                  : 'bg-red-500'
              }`}
              style={{
                width: `${Math.min(Math.abs(portfolioSummary.totalPnlPercent), 100)}%`
              }}
            />
          </div>
          
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
            <span>-100%</span>
            <span>0%</span>
            <span>+100%</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default PortfolioSummary;
