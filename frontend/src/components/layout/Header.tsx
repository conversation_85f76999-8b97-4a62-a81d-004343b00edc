import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Moon, 
  Sun, 
  RefreshCw, 
  Settings, 
  TrendingUp,
  ExternalLink 
} from 'lucide-react';
import { usePortfolioStore } from '../../store/portfolioStore';
import { fetchPricesDirectly } from '../../services/api';
import toast from 'react-hot-toast';

const Header: React.FC = () => {
  const { 
    userSettings, 
    toggleTheme, 
    coins, 
    updateMarketPrices, 
    setLoading,
    portfolioSummary 
  } = usePortfolioStore();

  const handleRefreshPrices = async () => {
    if (coins.length === 0) {
      toast.error('لا توجد عملات لتحديث أسعارها');
      return;
    }

    setLoading(true);
    const loadingToast = toast.loading('جاري تحديث الأسعار...');

    try {
      const symbols = coins.map(coin => coin.symbol);
      const prices = await fetchPricesDirectly(symbols);
      
      if (prices.length > 0) {
        updateMarketPrices(prices);
        toast.success(`تم تحديث ${prices.length} من ${symbols.length} عملة`, {
          id: loadingToast,
        });
      } else {
        toast.error('فشل في تحديث الأسعار', { id: loadingToast });
      }
    } catch (error) {
      console.error('Error refreshing prices:', error);
      toast.error('حدث خطأ أثناء تحديث الأسعار', { id: loadingToast });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatPercent = (percent: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(percent / 100);
  };

  return (
    <header className="bg-white dark:bg-gray-800 shadow-md border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* الشعار والعنوان */}
          <div className="flex items-center space-x-4 space-x-reverse">
            <Link to="/" className="flex items-center space-x-2 space-x-reverse">
              <TrendingUp className="h-8 w-8 text-primary-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  مدير الصفقات الشامل
                </h1>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  v2.0 - تداولجي
                </p>
              </div>
            </Link>
          </div>

          {/* ملخص المحفظة السريع */}
          {portfolioSummary && (
            <div className="hidden md:flex items-center space-x-6 space-x-reverse">
              <div className="text-center">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  إجمالي المستثمر
                </p>
                <p className="text-sm font-semibold text-gray-900 dark:text-white number-font">
                  {formatCurrency(portfolioSummary.totalInvested)}
                </p>
              </div>
              
              <div className="text-center">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  القيمة الحالية
                </p>
                <p className="text-sm font-semibold text-gray-900 dark:text-white number-font">
                  {formatCurrency(portfolioSummary.totalCurrentValue)}
                </p>
              </div>
              
              <div className="text-center">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  الربح/الخسارة
                </p>
                <p className={`text-sm font-semibold number-font ${
                  portfolioSummary.totalPnlAmount >= 0 
                    ? 'text-green-600 dark:text-green-400' 
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {formatCurrency(portfolioSummary.totalPnlAmount)}
                  <span className="mr-1">
                    ({formatPercent(portfolioSummary.totalPnlPercent)})
                  </span>
                </p>
              </div>
            </div>
          )}

          {/* أزرار التحكم */}
          <div className="flex items-center space-x-2 space-x-reverse">
            {/* زر تحديث الأسعار */}
            <button
              onClick={handleRefreshPrices}
              disabled={coins.length === 0}
              className="p-2 rounded-lg bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              title="تحديث الأسعار"
            >
              <RefreshCw className="h-5 w-5" />
            </button>

            {/* زر تبديل الثيم */}
            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
              title={userSettings.theme === 'light' ? 'الوضع المظلم' : 'الوضع الفاتح'}
            >
              {userSettings.theme === 'light' ? (
                <Moon className="h-5 w-5" />
              ) : (
                <Sun className="h-5 w-5" />
              )}
            </button>

            {/* زر الإعدادات */}
            <Link
              to="/settings"
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
              title="الإعدادات"
            >
              <Settings className="h-5 w-5" />
            </Link>

            {/* رابط التليغرام */}
            <a
              href="https://t.me/TadawulGY"
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-200"
              title="قناة التليغرام"
            >
              <ExternalLink className="h-5 w-5" />
            </a>
          </div>
        </div>
      </div>

      {/* شريط المعلومات الإضافية للشاشات الصغيرة */}
      {portfolioSummary && (
        <div className="md:hidden bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 px-4 py-2">
          <div className="flex justify-between items-center text-sm">
            <div className="text-center">
              <span className="text-gray-500 dark:text-gray-400">المستثمر: </span>
              <span className="font-semibold text-gray-900 dark:text-white number-font">
                {formatCurrency(portfolioSummary.totalInvested)}
              </span>
            </div>
            
            <div className="text-center">
              <span className="text-gray-500 dark:text-gray-400">الحالي: </span>
              <span className="font-semibold text-gray-900 dark:text-white number-font">
                {formatCurrency(portfolioSummary.totalCurrentValue)}
              </span>
            </div>
            
            <div className="text-center">
              <span className="text-gray-500 dark:text-gray-400">الربح: </span>
              <span className={`font-semibold number-font ${
                portfolioSummary.totalPnlAmount >= 0 
                  ? 'text-green-600 dark:text-green-400' 
                  : 'text-red-600 dark:text-red-400'
              }`}>
                {formatCurrency(portfolioSummary.totalPnlAmount)}
              </span>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
