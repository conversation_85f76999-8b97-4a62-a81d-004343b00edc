import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  TrendingUp, 
  BarChart3, 
  Settings, 
  Plus,
  ChevronLeft,
  ChevronRight,
  Coins
} from 'lucide-react';
import { usePortfolioStore } from '../../store/portfolioStore';
import AddCoinModal from '../modals/AddCoinModal';

const Sidebar: React.FC = () => {
  const location = useLocation();
  const { coins, activeCoinSymbol, setActiveCoin } = usePortfolioStore();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [showAddCoinModal, setShowAddCoinModal] = useState(false);

  const navigationItems = [
    {
      name: 'لوحة التحكم',
      href: '/',
      icon: Home,
    },
    {
      name: 'التحليلات',
      href: '/analytics',
      icon: BarChart3,
    },
    {
      name: 'الإعدادات',
      href: '/settings',
      icon: Settings,
    },
  ];

  const isActiveRoute = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  const handleCoinClick = (symbol: string) => {
    setActiveCoin(symbol);
  };

  return (
    <>
      <aside className={`fixed right-0 top-16 h-[calc(100vh-4rem)] bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 transition-all duration-300 z-40 ${
        isCollapsed ? 'w-16' : 'w-64'
      }`}>
        <div className="flex flex-col h-full">
          {/* زر الطي/التوسيع */}
          <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
            {!isCollapsed && (
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                القائمة
              </h2>
            )}
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
            >
              {isCollapsed ? (
                <ChevronLeft className="h-5 w-5 text-gray-500" />
              ) : (
                <ChevronRight className="h-5 w-5 text-gray-500" />
              )}
            </button>
          </div>

          {/* التنقل الرئيسي */}
          <nav className="flex-1 p-4 space-y-2">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = isActiveRoute(item.href);
              
              return (
                <Link
                  key={item.href}
                  to={item.href}
                  className={`flex items-center space-x-3 space-x-reverse px-3 py-2 rounded-lg transition-colors duration-200 ${
                    isActive
                      ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                  title={isCollapsed ? item.name : undefined}
                >
                  <Icon className="h-5 w-5 flex-shrink-0" />
                  {!isCollapsed && (
                    <span className="font-medium">{item.name}</span>
                  )}
                </Link>
              );
            })}
          </nav>

          {/* قسم العملات */}
          <div className="border-t border-gray-200 dark:border-gray-700">
            <div className="p-4">
              {!isCollapsed && (
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                    العملات المراقبة
                  </h3>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {coins.length}
                  </span>
                </div>
              )}

              {/* زر إضافة عملة جديدة */}
              <button
                onClick={() => setShowAddCoinModal(true)}
                className={`w-full flex items-center space-x-2 space-x-reverse px-3 py-2 rounded-lg bg-primary-600 hover:bg-primary-700 text-white transition-colors duration-200 ${
                  isCollapsed ? 'justify-center' : ''
                }`}
                title={isCollapsed ? 'إضافة عملة جديدة' : undefined}
              >
                <Plus className="h-4 w-4 flex-shrink-0" />
                {!isCollapsed && (
                  <span className="text-sm font-medium">إضافة عملة</span>
                )}
              </button>

              {/* قائمة العملات */}
              <div className="mt-3 space-y-1 max-h-64 overflow-y-auto">
                {coins.length === 0 ? (
                  !isCollapsed && (
                    <p className="text-xs text-gray-500 dark:text-gray-400 text-center py-4">
                      لا توجد عملات مضافة
                    </p>
                  )
                ) : (
                  coins.map((coin) => {
                    const isActive = activeCoinSymbol === coin.symbol;
                    
                    return (
                      <button
                        key={coin.symbol}
                        onClick={() => handleCoinClick(coin.symbol)}
                        className={`w-full flex items-center space-x-2 space-x-reverse px-3 py-2 rounded-lg transition-colors duration-200 ${
                          isActive
                            ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                            : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                        } ${isCollapsed ? 'justify-center' : ''}`}
                        title={isCollapsed ? coin.symbol : undefined}
                      >
                        <Coins className="h-4 w-4 flex-shrink-0" />
                        {!isCollapsed && (
                          <div className="flex-1 text-right">
                            <div className="text-sm font-medium">
                              {coin.symbol}
                            </div>
                            {coin.name && (
                              <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                                {coin.name}
                              </div>
                            )}
                          </div>
                        )}
                      </button>
                    );
                  })
                )}
              </div>
            </div>
          </div>

          {/* معلومات المطور */}
          {!isCollapsed && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="text-center">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  تم التصميم بواسطة
                </p>
                <p className="text-xs font-semibold text-gray-700 dark:text-gray-300">
                  المهندس معتز [تداولجي]
                </p>
                <a
                  href="https://t.me/TadawulGY"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-primary-600 dark:text-primary-400 hover:underline"
                >
                  @TadawulGY
                </a>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  نسألكم الدعاء بظهر الغيب 🤲
                </p>
              </div>
            </div>
          )}
        </div>
      </aside>

      {/* Modal إضافة عملة */}
      <AddCoinModal
        isOpen={showAddCoinModal}
        onClose={() => setShowAddCoinModal(false)}
      />

      {/* Overlay للشاشات الصغيرة */}
      {!isCollapsed && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setIsCollapsed(true)}
        />
      )}
    </>
  );
};

export default Sidebar;
