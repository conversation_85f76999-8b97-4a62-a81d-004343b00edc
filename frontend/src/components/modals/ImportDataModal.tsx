import React, { useState, useRef } from 'react';
import { X, Upload, FileText, AlertCircle, CheckCircle } from 'lucide-react';
import { usePortfolioStore } from '../../store/portfolioStore';
import type { CoinData } from '../../../../shared/types';
import toast from 'react-hot-toast';

interface ImportDataModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ImportDataModal: React.FC<ImportDataModalProps> = ({ isOpen, onClose }) => {
  const { importData, coins } = usePortfolioStore();
  const [isLoading, setIsLoading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [previewData, setPreviewData] = useState<CoinData[] | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = async (file: File) => {
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.name.endsWith('.json') && !file.name.endsWith('.txt')) {
      toast.error('يرجى اختيار ملف JSON أو TXT');
      return;
    }

    setIsLoading(true);

    try {
      const text = await file.text();
      let parsedData: CoinData[] = [];

      if (file.name.endsWith('.json')) {
        // معالجة ملف JSON
        const jsonData = JSON.parse(text);
        
        if (jsonData.coins && Array.isArray(jsonData.coins)) {
          parsedData = jsonData.coins;
        } else if (Array.isArray(jsonData)) {
          parsedData = jsonData;
        } else {
          throw new Error('تنسيق ملف JSON غير صحيح');
        }
      } else if (file.name.endsWith('.txt')) {
        // معالجة ملف TXT (النسخة القديمة)
        parsedData = parseTxtFile(text);
      }

      // التحقق من صحة البيانات
      const validatedData = validateImportData(parsedData);
      setPreviewData(validatedData);

    } catch (error) {
      console.error('Error parsing file:', error);
      toast.error('خطأ في قراءة الملف. تأكد من صحة التنسيق');
    } finally {
      setIsLoading(false);
    }
  };

  const parseTxtFile = (content: string): CoinData[] => {
    const lines = content.split('\n').filter(line => line.trim());
    const coins: CoinData[] = [];
    let currentCoin: Partial<CoinData> | null = null;

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      if (trimmedLine.startsWith('رمز:')) {
        // حفظ العملة السابقة إذا كانت موجودة
        if (currentCoin && currentCoin.symbol) {
          coins.push(currentCoin as CoinData);
        }
        
        // بدء عملة جديدة
        currentCoin = {
          symbol: trimmedLine.split(':')[1].trim(),
          repurchases: Array.from({ length: 10 }, () => ({ price: 0, amount: 0 })),
          targets: { tp1: 0, tp2: 0, tp3: 0, sl: 0 }
        };
      } else if (currentCoin) {
        if (trimmedLine.startsWith('سعر الدخول:')) {
          currentCoin.initialEntryPrice = parseFloat(trimmedLine.split(':')[1].trim()) || 0;
        } else if (trimmedLine.startsWith('المبلغ:')) {
          currentCoin.initialAmountDollars = parseFloat(trimmedLine.split(':')[1].trim()) || 0;
        } else if (trimmedLine.startsWith('تعزيز')) {
          const parts = trimmedLine.split(':')[1].split(',');
          if (parts.length === 2) {
            const index = parseInt(trimmedLine.match(/\d+/)?.[0] || '1') - 1;
            if (index >= 0 && index < 10) {
              currentCoin.repurchases![index] = {
                price: parseFloat(parts[0].trim()) || 0,
                amount: parseFloat(parts[1].trim()) || 0
              };
            }
          }
        } else if (trimmedLine.startsWith('هدف1:')) {
          currentCoin.targets!.tp1 = parseFloat(trimmedLine.split(':')[1].trim()) || 0;
        } else if (trimmedLine.startsWith('هدف2:')) {
          currentCoin.targets!.tp2 = parseFloat(trimmedLine.split(':')[1].trim()) || 0;
        } else if (trimmedLine.startsWith('هدف3:')) {
          currentCoin.targets!.tp3 = parseFloat(trimmedLine.split(':')[1].trim()) || 0;
        } else if (trimmedLine.startsWith('وقف:')) {
          currentCoin.targets!.sl = parseFloat(trimmedLine.split(':')[1].trim()) || 0;
        }
      }
    }

    // حفظ العملة الأخيرة
    if (currentCoin && currentCoin.symbol) {
      coins.push(currentCoin as CoinData);
    }

    return coins;
  };

  const validateImportData = (data: any[]): CoinData[] => {
    return data
      .filter(item => item && typeof item === 'object')
      .map(item => ({
        symbol: item.symbol || '',
        initialEntryPrice: Number(item.initialEntryPrice) || 0,
        initialAmountDollars: Number(item.initialAmountDollars) || 0,
        repurchases: Array.isArray(item.repurchases) 
          ? item.repurchases.slice(0, 10).map((rep: any) => ({
              price: Number(rep.price) || 0,
              amount: Number(rep.amount) || 0
            }))
          : Array.from({ length: 10 }, () => ({ price: 0, amount: 0 })),
        targets: {
          tp1: Number(item.targets?.tp1) || 0,
          tp2: Number(item.targets?.tp2) || 0,
          tp3: Number(item.targets?.tp3) || 0,
          sl: Number(item.targets?.sl) || 0
        }
      }))
      .filter(coin => coin.symbol && coin.initialEntryPrice > 0 && coin.initialAmountDollars > 0);
  };

  const handleImport = () => {
    if (!previewData || previewData.length === 0) {
      toast.error('لا توجد بيانات صالحة للاستيراد');
      return;
    }

    // التحقق من العملات المكررة
    const existingSymbols = coins.map(coin => coin.symbol);
    const duplicates = previewData.filter(coin => existingSymbols.includes(coin.symbol));
    
    if (duplicates.length > 0) {
      const confirmed = window.confirm(
        `توجد ${duplicates.length} عملة مكررة. هل تريد استبدالها؟`
      );
      if (!confirmed) return;
    }

    importData(previewData);
    toast.success(`تم استيراد ${previewData.length} عملة بنجاح`);
    handleClose();
  };

  const handleClose = () => {
    setPreviewData(null);
    setDragActive(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            استيراد البيانات
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          {!previewData ? (
            <>
              {/* منطقة رفع الملف */}
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-300 dark:border-gray-600'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  اسحب وأفلت الملف هنا
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  أو انقر لاختيار ملف
                </p>
                
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".json,.txt"
                  onChange={handleFileInput}
                  className="hidden"
                />
                
                <button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isLoading}
                  className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 transition-colors"
                >
                  {isLoading ? 'جاري القراءة...' : 'اختيار ملف'}
                </button>
              </div>

              {/* معلومات التنسيقات المدعومة */}
              <div className="mt-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <div className="flex items-start space-x-3 space-x-reverse">
                  <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-900 dark:text-blue-300">
                      التنسيقات المدعومة
                    </h4>
                    <ul className="text-sm text-blue-800 dark:text-blue-400 mt-2 space-y-1">
                      <li>• ملفات JSON من التطبيق الحديث</li>
                      <li>• ملفات TXT من النسخة القديمة</li>
                      <li>• ملفات JSON المصدرة من تطبيقات أخرى</li>
                    </ul>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <>
              {/* معاينة البيانات */}
              <div className="mb-6">
                <div className="flex items-center space-x-2 space-x-reverse mb-4">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    معاينة البيانات المستوردة
                  </h3>
                </div>
                
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-64 overflow-y-auto">
                  <div className="space-y-3">
                    {previewData.map((coin, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between bg-white dark:bg-gray-800 rounded p-3"
                      >
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            {coin.symbol}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            سعر الدخول: ${coin.initialEntryPrice} | المبلغ: ${coin.initialAmountDollars}
                          </div>
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {coins.some(c => c.symbol === coin.symbol) && (
                            <span className="text-orange-600 dark:text-orange-400">مكرر</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                  سيتم استيراد {previewData.length} عملة
                </div>
              </div>

              {/* أزرار التحكم */}
              <div className="flex space-x-3 space-x-reverse">
                <button
                  onClick={() => setPreviewData(null)}
                  className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  اختيار ملف آخر
                </button>
                <button
                  onClick={handleImport}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  استيراد البيانات
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImportDataModal;
