import React, { useState } from 'react';
import { X, Search, Plus } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { usePortfolioStore } from '../../store/portfolioStore';
import { fetchPricesDirectly } from '../../services/api';
import toast from 'react-hot-toast';

interface AddCoinModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// Schema للتحقق من صحة البيانات
const coinSchema = z.object({
  symbol: z.string()
    .min(1, 'رمز العملة مطلوب')
    .regex(/^[A-Z]+USDT?$/, 'رمز العملة يجب أن ينتهي بـ USDT أو USDT'),
  initialEntryPrice: z.number()
    .positive('سعر الدخول يجب أن يكون أكبر من صفر'),
  initialAmountDollars: z.number()
    .positive('المبلغ يجب أن يكون أكبر من صفر'),
  tp1: z.number().optional(),
  tp2: z.number().optional(),
  tp3: z.number().optional(),
  sl: z.number().optional(),
});

type CoinFormData = z.infer<typeof coinSchema>;

const AddCoinModal: React.FC<AddCoinModalProps> = ({ isOpen, onClose }) => {
  const { addCoin, coins } = usePortfolioStore();
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<CoinFormData>({
    resolver: zodResolver(coinSchema)
  });

  const symbolValue = watch('symbol');

  // قائمة العملات الشائعة
  const popularCoins = [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
    'SOLUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'MATICUSDT',
    'LINKUSDT', 'LTCUSDT', 'UNIUSDT', 'ATOMUSDT', 'VETUSDT'
  ];

  const handleSymbolSearch = (value: string) => {
    if (value.length < 2) {
      setSearchResults([]);
      return;
    }

    const filtered = popularCoins.filter(coin =>
      coin.toLowerCase().includes(value.toLowerCase())
    );
    setSearchResults(filtered.slice(0, 5));
  };

  const handleSymbolSelect = (symbol: string) => {
    setValue('symbol', symbol);
    setSearchResults([]);
  };

  const onSubmit = async (data: CoinFormData) => {
    // التحقق من عدم وجود العملة مسبقاً
    if (coins.some(coin => coin.symbol === data.symbol)) {
      toast.error('هذه العملة موجودة بالفعل في المحفظة');
      return;
    }

    setIsLoading(true);
    const loadingToast = toast.loading('جاري إضافة العملة...');

    try {
      // محاولة جلب السعر الحالي للتحقق من صحة رمز العملة
      const prices = await fetchPricesDirectly([data.symbol]);
      
      if (prices.length === 0) {
        toast.error('لم يتم العثور على هذه العملة في البورصات المدعومة', {
          id: loadingToast
        });
        return;
      }

      // إنشاء بيانات العملة الجديدة
      const newCoin = {
        symbol: data.symbol,
        initialEntryPrice: data.initialEntryPrice,
        initialAmountDollars: data.initialAmountDollars,
        repurchases: Array.from({ length: 10 }, () => ({ price: 0, amount: 0 })),
        targets: {
          tp1: data.tp1 || 0,
          tp2: data.tp2 || 0,
          tp3: data.tp3 || 0,
          sl: data.sl || 0
        }
      };

      addCoin(newCoin);
      
      toast.success(`تمت إضافة ${data.symbol} بنجاح`, {
        id: loadingToast
      });

      reset();
      onClose();
    } catch (error) {
      console.error('Error adding coin:', error);
      toast.error('حدث خطأ أثناء إضافة العملة', {
        id: loadingToast
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    setSearchResults([]);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            إضافة عملة جديدة
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-4">
          {/* رمز العملة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              رمز العملة
            </label>
            <div className="relative">
              <input
                type="text"
                {...register('symbol')}
                placeholder="مثال: BTCUSDT"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                onChange={(e) => {
                  const value = e.target.value.toUpperCase();
                  setValue('symbol', value);
                  handleSymbolSearch(value);
                }}
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
            {errors.symbol && (
              <p className="text-red-500 text-sm mt-1">{errors.symbol.message}</p>
            )}
            
            {/* نتائج البحث */}
            {searchResults.length > 0 && (
              <div className="mt-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg">
                {searchResults.map((symbol) => (
                  <button
                    key={symbol}
                    type="button"
                    onClick={() => handleSymbolSelect(symbol)}
                    className="w-full text-right px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-900 dark:text-white first:rounded-t-lg last:rounded-b-lg"
                  >
                    {symbol}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* العملات الشائعة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              العملات الشائعة
            </label>
            <div className="grid grid-cols-3 gap-2">
              {popularCoins.slice(0, 6).map((symbol) => (
                <button
                  key={symbol}
                  type="button"
                  onClick={() => handleSymbolSelect(symbol)}
                  className="px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  {symbol}
                </button>
              ))}
            </div>
          </div>

          {/* سعر الدخول */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              سعر الدخول الأولي ($)
            </label>
            <input
              type="number"
              step="any"
              {...register('initialEntryPrice', { valueAsNumber: true })}
              placeholder="0.00"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white number-font"
            />
            {errors.initialEntryPrice && (
              <p className="text-red-500 text-sm mt-1">{errors.initialEntryPrice.message}</p>
            )}
          </div>

          {/* المبلغ المستثمر */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              المبلغ المستثمر ($)
            </label>
            <input
              type="number"
              step="any"
              {...register('initialAmountDollars', { valueAsNumber: true })}
              placeholder="0.00"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white number-font"
            />
            {errors.initialAmountDollars && (
              <p className="text-red-500 text-sm mt-1">{errors.initialAmountDollars.message}</p>
            )}
          </div>

          {/* الأهداف (اختيارية) */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              الأهداف (اختيارية)
            </h3>
            
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                  هدف 1 (%)
                </label>
                <input
                  type="number"
                  step="any"
                  {...register('tp1', { valueAsNumber: true })}
                  placeholder="10"
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white number-font"
                />
              </div>
              
              <div>
                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                  هدف 2 (%)
                </label>
                <input
                  type="number"
                  step="any"
                  {...register('tp2', { valueAsNumber: true })}
                  placeholder="25"
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white number-font"
                />
              </div>
              
              <div>
                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                  هدف 3 (%)
                </label>
                <input
                  type="number"
                  step="any"
                  {...register('tp3', { valueAsNumber: true })}
                  placeholder="50"
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white number-font"
                />
              </div>
              
              <div>
                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                  وقف الخسارة (%)
                </label>
                <input
                  type="number"
                  step="any"
                  {...register('sl', { valueAsNumber: true })}
                  placeholder="10"
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white number-font"
                />
              </div>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex space-x-3 space-x-reverse pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2 space-x-reverse"
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Plus className="h-4 w-4" />
              )}
              <span>{isLoading ? 'جاري الإضافة...' : 'إضافة العملة'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddCoinModal;
