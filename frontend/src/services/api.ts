import axios from 'axios';
import type { 
  CoinData, 
  MarketPrice, 
  PortfolioSummary,
  CoinSummary,
  ApiResponse,
  GetCoinsResponse,
  GetCoinResponse,
  CreateCoinResponse,
  UpdateCoinResponse,
  DeleteCoinResponse,
  GetPricesResponse,
  GetSummaryResponse
} from '../../../shared/types';

// إعداد Axios
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptors للتعامل مع الأخطاء
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    
    if (error.response?.status === 404) {
      throw new Error('الخدمة غير متوفرة حالياً');
    } else if (error.response?.status >= 500) {
      throw new Error('خطأ في الخادم، يرجى المحاولة لاحقاً');
    } else if (error.code === 'ECONNABORTED') {
      throw new Error('انتهت مهلة الاتصال');
    } else {
      throw new Error(error.response?.data?.message || 'حدث خطأ غير متوقع');
    }
  }
);

// خدمات العملات
export const coinsApi = {
  // جلب جميع العملات
  getAll: async (): Promise<CoinData[]> => {
    try {
      const response = await api.get<GetCoinsResponse>('/coins');
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching coins:', error);
      return [];
    }
  },

  // جلب عملة واحدة
  getOne: async (symbol: string): Promise<CoinData | null> => {
    try {
      const response = await api.get<GetCoinResponse>(`/coins/${symbol}`);
      return response.data.data || null;
    } catch (error) {
      console.error(`Error fetching coin ${symbol}:`, error);
      return null;
    }
  },

  // إضافة عملة جديدة
  create: async (coin: Omit<CoinData, 'id' | 'createdAt' | 'updatedAt'>): Promise<CoinData> => {
    const response = await api.post<CreateCoinResponse>('/coins', coin);
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'فشل في إضافة العملة');
    }
    return response.data.data;
  },

  // تحديث عملة
  update: async (symbol: string, updates: Partial<CoinData>): Promise<CoinData> => {
    const response = await api.put<UpdateCoinResponse>(`/coins/${symbol}`, updates);
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'فشل في تحديث العملة');
    }
    return response.data.data;
  },

  // حذف عملة
  delete: async (symbol: string): Promise<boolean> => {
    const response = await api.delete<DeleteCoinResponse>(`/coins/${symbol}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'فشل في حذف العملة');
    }
    return true;
  },
};

// خدمات الأسعار
export const pricesApi = {
  // جلب أسعار جميع العملات المراقبة
  getAll: async (symbols: string[]): Promise<MarketPrice[]> => {
    try {
      const response = await api.post<GetPricesResponse>('/prices', { symbols });
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching prices:', error);
      return [];
    }
  },

  // جلب سعر عملة واحدة
  getOne: async (symbol: string): Promise<MarketPrice | null> => {
    try {
      const response = await api.get<ApiResponse<MarketPrice>>(`/prices/${symbol}`);
      return response.data.data || null;
    } catch (error) {
      console.error(`Error fetching price for ${symbol}:`, error);
      return null;
    }
  },
};

// خدمات الملخص والتحليلات
export const analyticsApi = {
  // جلب ملخص المحفظة
  getPortfolioSummary: async (): Promise<PortfolioSummary | null> => {
    try {
      const response = await api.get<GetSummaryResponse>('/analytics/portfolio-summary');
      return response.data.data || null;
    } catch (error) {
      console.error('Error fetching portfolio summary:', error);
      return null;
    }
  },

  // جلب ملخص العملات
  getCoinsSummary: async (): Promise<CoinSummary[]> => {
    try {
      const response = await api.get<ApiResponse<CoinSummary[]>>('/analytics/coins-summary');
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching coins summary:', error);
      return [];
    }
  },
};

// خدمات التصدير والاستيراد
export const dataApi = {
  // تصدير البيانات
  exportData: async (): Promise<Blob> => {
    const response = await api.get('/data/export', {
      responseType: 'blob',
    });
    return response.data;
  },

  // استيراد البيانات
  importData: async (file: File): Promise<boolean> => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post<ApiResponse<boolean>>('/data/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    if (!response.data.success) {
      throw new Error(response.data.error || 'فشل في استيراد البيانات');
    }
    
    return true;
  },
};

// خدمة فحص حالة الخادم
export const healthApi = {
  check: async (): Promise<boolean> => {
    try {
      const response = await api.get('/health');
      return response.status === 200;
    } catch (error) {
      return false;
    }
  },
};

// خدمات محلية للعمل بدون خادم (Fallback)
export const localStorageApi = {
  // مفتاح التخزين المحلي
  STORAGE_KEY: 'crypto-portfolio-data-v2',

  // حفظ البيانات محلياً
  saveCoins: (coins: CoinData[]): void => {
    try {
      const data = {
        coins,
        timestamp: new Date().toISOString(),
        version: '2.0.0'
      };
      localStorage.setItem(localStorageApi.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  },

  // جلب البيانات محلياً
  loadCoins: (): CoinData[] => {
    try {
      const data = localStorage.getItem(localStorageApi.STORAGE_KEY);
      if (data) {
        const parsed = JSON.parse(data);
        return parsed.coins || [];
      }
    } catch (error) {
      console.error('Error loading from localStorage:', error);
    }
    return [];
  },

  // مسح البيانات المحلية
  clearData: (): void => {
    localStorage.removeItem(localStorageApi.STORAGE_KEY);
  },
};

// دالة مساعدة للتحقق من توفر الخادم
export const checkServerAvailability = async (): Promise<boolean> => {
  return await healthApi.check();
};

// دالة مساعدة لجلب الأسعار من مصادر خارجية مباشرة
export const fetchPricesDirectly = async (symbols: string[]): Promise<MarketPrice[]> => {
  const prices: MarketPrice[] = [];
  
  for (const symbol of symbols) {
    try {
      // محاولة جلب السعر من Binance
      const binanceResponse = await fetch(`https://api.binance.com/api/v3/ticker/price?symbol=${symbol}`);
      if (binanceResponse.ok) {
        const data = await binanceResponse.json();
        if (data.price) {
          prices.push({
            symbol,
            price: parseFloat(data.price),
            source: 'binance',
            timestamp: new Date(),
          });
          continue;
        }
      }

      // إذا فشل Binance، جرب KuCoin
      const kucoinSymbol = symbol.endsWith('USDT') ? `${symbol.slice(0, -4)}-USDT` : symbol;
      const kucoinResponse = await fetch(`https://api.kucoin.com/api/v1/market/orderbook/level1?symbol=${kucoinSymbol}`);
      if (kucoinResponse.ok) {
        const data = await kucoinResponse.json();
        if (data.code === '200000' && data.data?.price) {
          prices.push({
            symbol,
            price: parseFloat(data.data.price),
            source: 'kucoin',
            timestamp: new Date(),
          });
        }
      }
    } catch (error) {
      console.error(`Error fetching price for ${symbol}:`, error);
    }
  }
  
  return prices;
};
