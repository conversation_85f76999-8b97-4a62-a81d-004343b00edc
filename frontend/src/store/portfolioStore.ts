import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { 
  CoinData, 
  MarketPrice, 
  PortfolioSummary, 
  CoinSummary,
  UserSettings,
  SortConfig,
  FilterConfig 
} from '../../../shared/types';

interface PortfolioState {
  // البيانات الأساسية
  coins: CoinData[];
  marketPrices: Record<string, MarketPrice>;
  activeCoinSymbol: string | null;
  portfolioSummary: PortfolioSummary | null;
  coinsSummary: CoinSummary[];
  
  // حالة التطبيق
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  
  // إعدادات المستخدم
  userSettings: UserSettings;
  
  // فلترة وترتيب
  sortConfig: SortConfig;
  filterConfig: FilterConfig;
  
  // الأفعال - إدارة العملات
  addCoin: (coin: Omit<CoinData, 'id'>) => void;
  updateCoin: (symbol: string, updates: Partial<CoinData>) => void;
  deleteCoin: (symbol: string) => void;
  setActiveCoin: (symbol: string | null) => void;
  
  // الأفعال - الأسعار
  updateMarketPrices: (prices: MarketPrice[]) => void;
  updateSinglePrice: (price: MarketPrice) => void;
  
  // الأفعال - الملخص
  updatePortfolioSummary: (summary: PortfolioSummary) => void;
  updateCoinsSummary: (summary: CoinSummary[]) => void;
  
  // الأفعال - حالة التطبيق
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // الأفعال - الإعدادات
  updateUserSettings: (settings: Partial<UserSettings>) => void;
  toggleTheme: () => void;
  
  // الأفعال - الفلترة والترتيب
  setSortConfig: (config: SortConfig) => void;
  setFilterConfig: (config: FilterConfig) => void;
  
  // الأفعال - المساعدة
  resetStore: () => void;
  importData: (data: CoinData[]) => void;
}

const defaultUserSettings: UserSettings = {
  theme: 'light',
  language: 'ar',
  autoRefresh: true,
  refreshInterval: 30,
  notifications: true,
  soundAlerts: false,
  defaultCurrency: 'USD'
};

const defaultSortConfig: SortConfig = {
  field: 'symbol',
  direction: 'asc'
};

const defaultFilterConfig: FilterConfig = {
  showProfitable: true,
  showLosing: true
};

export const usePortfolioStore = create<PortfolioState>()(
  devtools(
    persist(
      (set, get) => ({
        // الحالة الأولية
        coins: [],
        marketPrices: {},
        activeCoinSymbol: null,
        portfolioSummary: null,
        coinsSummary: [],
        isLoading: false,
        error: null,
        lastUpdated: null,
        userSettings: defaultUserSettings,
        sortConfig: defaultSortConfig,
        filterConfig: defaultFilterConfig,

        // إدارة العملات
        addCoin: (coin) => {
          set((state) => {
            const newCoin: CoinData = {
              ...coin,
              id: crypto.randomUUID(),
              createdAt: new Date(),
              updatedAt: new Date()
            };
            
            return {
              coins: [...state.coins, newCoin],
              activeCoinSymbol: newCoin.symbol
            };
          });
        },

        updateCoin: (symbol, updates) => {
          set((state) => ({
            coins: state.coins.map(coin => 
              coin.symbol === symbol 
                ? { ...coin, ...updates, updatedAt: new Date() }
                : coin
            )
          }));
        },

        deleteCoin: (symbol) => {
          set((state) => {
            const newCoins = state.coins.filter(coin => coin.symbol !== symbol);
            const newMarketPrices = { ...state.marketPrices };
            delete newMarketPrices[symbol];
            
            return {
              coins: newCoins,
              marketPrices: newMarketPrices,
              activeCoinSymbol: state.activeCoinSymbol === symbol 
                ? (newCoins.length > 0 ? newCoins[0].symbol : null)
                : state.activeCoinSymbol
            };
          });
        },

        setActiveCoin: (symbol) => {
          set({ activeCoinSymbol: symbol });
        },

        // إدارة الأسعار
        updateMarketPrices: (prices) => {
          set((state) => {
            const newPrices = { ...state.marketPrices };
            prices.forEach(price => {
              newPrices[price.symbol] = price;
            });
            
            return {
              marketPrices: newPrices,
              lastUpdated: new Date()
            };
          });
        },

        updateSinglePrice: (price) => {
          set((state) => ({
            marketPrices: {
              ...state.marketPrices,
              [price.symbol]: price
            },
            lastUpdated: new Date()
          }));
        },

        // إدارة الملخص
        updatePortfolioSummary: (summary) => {
          set({ portfolioSummary: summary });
        },

        updateCoinsSummary: (summary) => {
          set({ coinsSummary: summary });
        },

        // إدارة حالة التطبيق
        setLoading: (loading) => {
          set({ isLoading: loading });
        },

        setError: (error) => {
          set({ error });
        },

        clearError: () => {
          set({ error: null });
        },

        // إدارة الإعدادات
        updateUserSettings: (settings) => {
          set((state) => ({
            userSettings: { ...state.userSettings, ...settings }
          }));
        },

        toggleTheme: () => {
          set((state) => ({
            userSettings: {
              ...state.userSettings,
              theme: state.userSettings.theme === 'light' ? 'dark' : 'light'
            }
          }));
        },

        // الفلترة والترتيب
        setSortConfig: (config) => {
          set({ sortConfig: config });
        },

        setFilterConfig: (config) => {
          set({ filterConfig: config });
        },

        // المساعدة
        resetStore: () => {
          set({
            coins: [],
            marketPrices: {},
            activeCoinSymbol: null,
            portfolioSummary: null,
            coinsSummary: [],
            error: null,
            lastUpdated: null
          });
        },

        importData: (data) => {
          set({
            coins: data,
            activeCoinSymbol: data.length > 0 ? data[0].symbol : null
          });
        }
      }),
      {
        name: 'crypto-portfolio-storage',
        partialize: (state) => ({
          coins: state.coins,
          userSettings: state.userSettings,
          activeCoinSymbol: state.activeCoinSymbol,
          sortConfig: state.sortConfig,
          filterConfig: state.filterConfig
        })
      }
    ),
    { name: 'portfolio-store' }
  )
);
