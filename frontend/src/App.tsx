import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { usePortfolioStore } from './store/portfolioStore';
import Header from './components/layout/Header';
import Sidebar from './components/layout/Sidebar';
import Dashboard from './pages/Dashboard';
import CoinDetails from './pages/CoinDetails';
import Analytics from './pages/Analytics';
import Settings from './pages/Settings';
import { checkServerAvailability } from './services/api';

function App() {
  const { userSettings, setError } = usePortfolioStore();

  useEffect(() => {
    // تطبيق الثيم
    if (userSettings.theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [userSettings.theme]);

  useEffect(() => {
    // فحص توفر الخادم
    const checkServer = async () => {
      try {
        const isAvailable = await checkServerAvailability();
        if (!isAvailable) {
          console.warn('Server not available, using local storage mode');
        }
      } catch (error) {
        console.error('Error checking server availability:', error);
        setError('فشل في الاتصال بالخادم، سيتم استخدام التخزين المحلي');
      }
    };

    checkServer();
  }, [setError]);

  return (
    <Router>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
        <Header />

        <div className="flex">
          <Sidebar />

          <main className="flex-1 lg:mr-64">
            <div className="container mx-auto px-4 py-6">
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/coin/:symbol" element={<CoinDetails />} />
                <Route path="/analytics" element={<Analytics />} />
                <Route path="/settings" element={<Settings />} />
              </Routes>
            </div>
          </main>
        </div>

        {/* Toast notifications */}
        <Toaster
          position="top-center"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
              fontFamily: 'Noto Sans Arabic, sans-serif',
              direction: 'rtl',
            },
            success: {
              style: {
                background: '#22c55e',
              },
            },
            error: {
              style: {
                background: '#ef4444',
              },
            },
          }}
        />
      </div>
    </Router>
  );
}

export default App;
