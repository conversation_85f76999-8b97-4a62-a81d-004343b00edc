<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متابعة الصفقات الشاملة - تداولجي (النسخة القديمة)</title>
    <!-- هذا الملف سيتم استبداله بالتطبيق الجديد المبني بـ React -->
    <style>
        :root {
            /* Updated Light Theme Colors (from File 2) */
            --bg-color: #f4f6f9;
            --section-bg: #ffffff;
            --text-color: #212529;
            --text-muted: #6c757d;
            --text-light: #f8f9fa; /* Used for text on dark backgrounds like page-header */
            --primary-color: #0056b3;
            --secondary-color: #007bff;
            --border-color: #e0e0e0;
            --input-bg: #ffffff;
            --input-border: #ced4da;
            --hover-bg: #e9ecef;
            --table-header-bg: #f8f9fa;
            --dark-header-bg: #343a40; /* Page top header */
            --positive-color: #198754;
            --negative-color: #dc3545;
            --link-color: #0d6efd;
            --link-hover-color: #0a58ca;
            --header-link-color: #adb5bd;
            --header-link-hover-color: #f8f9fa;
            --shadow-color: rgba(0,0,0,0.05);
            --output-field-bg: #eef1f5;
            --output-field-text: #495057;
            --sl-output-bg: #f8d7da;
            --sl-output-text: #721c24;
            --sl-output-border: #f1c0c5;
            --telegram-link-color: #4da6ff;
            --focus-ring-color: rgba(0, 123, 255, 0.25);
            --font-weight-bold: 700;
            --font-weight-semibold: 600;
            --font-weight-normal: 500;
        }

        .dark-mode {
             /* Original Dark Theme Overrides (from v6 equivalent - as per user request, largely matches File 2) */
            --bg-color: #121212;
            --section-bg: #1e1e1e;
            --text-color: #e0e0e0;
            --text-muted: #888;
            --text-light: #f2f2f2; /* Kept from File 1 for text on dark backgrounds, File 2's dark #121212 was unsuitable */
            --primary-color: #4dabf7;
            --secondary-color: #74c0fc;
            --border-color: #333;
            --input-bg: #2a2a2a;
            --input-border: #444;
            --hover-bg: #333;
            --table-header-bg: #2a2a2a;
            --dark-header-bg: #212529;
            --positive-color: #20c997;
            --negative-color: #fa5252;
            --link-color: #4dabf7;
            --link-hover-color: #74c0fc;
            --header-link-color: #9ab;
            --header-link-hover-color: #cde;
            --telegram-link-color: #6699cc; /* File 2 Dark Telegram color */
            --shadow-color: rgba(255,255,255,0.08);
            --output-field-bg: #2c2c2c;
            --output-field-text: #ccc;
            --sl-output-bg: #5c2a30;
            --sl-output-text: #ffc0c7;
            --sl-output-border: #8a3f47;
            --focus-ring-color: rgba(77, 171, 247, 0.3);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6; margin: 0; font-size: 14px;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .page-header {
            text-align: center; padding: 15px 20px;
            background-color: var(--dark-header-bg); color: var(--text-light); /* Using File 1's --text-light which is suitable for dark bg */
            font-size: 1em; border-bottom: 4px solid var(--primary-color);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1); /* Matches File 2 implicit general shadow if vars updated */
        }
        .page-header strong { font-weight: var(--font-weight-semibold); }
        .page-header a {
            color: var(--header-link-color); text-decoration: none;
            font-weight: var(--font-weight-normal); margin: 0 5px;
            transition: color 0.2s ease;
        }
        .page-header a:hover { text-decoration: underline; color: var(--header-link-hover-color); }
        .page-header a.telegram-link {
            color: var(--telegram-link-color) !important;
            font-weight: var(--font-weight-bold) !important;
        }
        .page-header a.telegram-link:hover { text-decoration: underline; filter: brightness(1.1); }

        .container { max-width: 1920px; margin: 20px auto; padding: 0 25px; }
        h1 { text-align: center; color: var(--primary-color); margin-top: 15px; margin-bottom: 30px; font-weight: var(--font-weight-bold); font-size: 1.8em; transition: color 0.3s ease; }
        h2 { margin-top: 0; color: var(--primary-color); font-size: 1.4em; border-bottom: 2px solid var(--secondary-color); padding-bottom: 10px; margin-bottom: 25px; font-weight: var(--font-weight-semibold); transition: color 0.3s ease, border-color 0.3s ease; }
        h3 { font-size: 1.2em; color: var(--primary-color); margin-bottom: 15px; margin-top: 0; font-weight: var(--font-weight-semibold); transition: color 0.3s ease;}

        .overall-summary-section {
            background-color: var(--section-bg); padding: 20px 25px; margin-bottom: 25px;
            border-radius: 10px; /* From File 2 */
            box-shadow: 0 4px 12px var(--shadow-color); /* From File 2 */
            position: sticky; top: 0; z-index: 1000; border: 1px solid var(--border-color);
            transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
        }
        .summary-table-container {
            max-height: 280px; overflow-y: auto; border: 1px solid var(--border-color);
            border-radius: 6px; /* From File 2 */
            margin-bottom: 20px; /* From File 2 */
            background-color: var(--bg-color); /* From File 2 */
            transition: border-color 0.3s ease, background-color 0.3s ease;
        }
        .summary-table {
            width: 100%;
            border-collapse: separate; /* From File 2 */
            border-spacing: 0; /* From File 2 */
            font-size: 0.9em;
        }
        .summary-table th, .summary-table td {
            border-bottom: 1px solid var(--border-color); /* From File 2 */
            padding: 10px 14px; /* From File 2 */
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            transition: border-color 0.3s ease, background-color 0.2s ease;
        }
        .summary-table th {
            background-color: var(--table-header-bg); color: var(--text-color); position: sticky; top: 0; z-index: 1;
            font-weight: var(--font-weight-semibold);
            border-top: 1px solid var(--border-color); /* From File 2 */
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        /* Added from File 2 for table cell borders and radii */
        .summary-table td:first-child, .summary-table th:first-child { border-right: 1px solid var(--border-color); border-top-right-radius: 6px; }
        .summary-table td:last-child, .summary-table th:last-child { border-left: 1px solid var(--border-color); border-top-left-radius: 6px;}
        .summary-table tr:last-child td { border-bottom: none; }
        .summary-table tr:last-child td:first-child { border-bottom-right-radius: 6px; }
        .summary-table tr:last-child td:last-child { border-bottom-left-radius: 6px; }

        .summary-table tr:nth-child(even) { background-color: transparent; } /* Removed zebra striping to match File 2 */
        .summary-table tr:hover td { background-color: var(--hover-bg); }
        .summary-table tbody td { font-weight: var(--font-weight-normal); /* From File 2 */ }
        .summary-table .coin-symbol { text-align: right; padding-right: 15px; font-weight: var(--font-weight-semibold); }
        .summary-table .number-col { font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace; direction: ltr; text-align: center; font-weight: var(--font-weight-semibold); }
        .summary-table .pnl-positive, .totals-positive { color: var(--positive-color) !important; font-weight: var(--font-weight-bold); }
        .summary-table .pnl-negative, .totals-negative { color: var(--negative-color) !important; font-weight: var(--font-weight-bold); }
        .summary-table .error { color: var(--negative-color); font-style: italic; font-size: 0.9em; font-weight: var(--font-weight-normal); }
        .summary-coin-clickable { cursor: pointer; transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out; }
        .summary-coin-clickable:hover { background-color: var(--hover-bg); color: var(--primary-color); font-weight: var(--font-weight-bold); }


        .summary-totals {
            display: grid; /* From File 2 */
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); /* From File 2 */
            gap: 15px; /* From File 2 (was 15px in F1 too but now with grid) */
            background-color: var(--section-bg); /* From File 2 */
            padding: 15px 20px; /* From File 2 */
            border-radius: 8px; /* From File 2 */
            margin-top: 15px; /* From File 2 */
            border: 1px solid var(--border-color); font-weight: var(--font-weight-bold); text-align: center;
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }
        .summary-totals div { display: flex; flex-direction: column; flex-basis: auto; flex-grow: auto; /* Adjusted from File 1 to suit grid */ }
        .summary-totals label {
            font-size: 0.9em; color: var(--text-muted);
            margin-bottom: 5px; /* From File 2 */
            font-weight: var(--font-weight-normal); transition: color 0.3s ease;
        }
        .summary-totals span {
            font-size: 1.3em; /* From File 2 */
            font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace; font-weight: var(--font-weight-bold); transition: color 0.3s ease;
        }

        .controls-bar {
            display: flex;
            gap: 12px; /* From File 2 */
            margin-bottom: 30px; /* From File 2 */
            flex-wrap: wrap; background-color: var(--section-bg);
            padding: 15px 20px; /* From File 2 */
            border-radius: 8px;
            box-shadow: 0 2px 8px var(--shadow-color); /* From File 2 */
            border: 1px solid var(--border-color); align-items: center;
            transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
        }
        .controls-bar label { font-weight: var(--font-weight-semibold); margin-left: 5px; color: var(--primary-color); flex-shrink: 0; font-size: 0.95em; }
        .controls-bar select, .controls-bar input[type="text"] {
            padding: 10px 14px; /* From File 2 */
            border: 1px solid var(--input-border);
            border-radius: 6px; /* From File 2 */
            font-size: 1em; flex-grow: 1;
            min-width: 160px; /* From File 2 */
            background-color: var(--input-bg); color: var(--text-color);
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.06); /* From File 2 */
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.2s ease, box-shadow 0.2s ease;
        }
        .controls-bar select:focus, .controls-bar input[type="text"]:focus { outline: none; border-color: var(--primary-color); box-shadow: 0 0 0 3px var(--focus-ring-color), inset 0 1px 3px rgba(0,0,0,0.06); }
        .controls-bar button {
            padding: 10px 20px; /* From File 2 */
            font-size: 0.95em; font-weight: var(--font-weight-semibold); cursor: pointer; background-color: var(--secondary-color); color: white; border: none;
            border-radius: 6px; /* From File 2 */
            flex-shrink: 0; transition: background-color 0.2s ease, transform 0.1s ease;
        }
        .controls-bar button:hover { background-color: var(--primary-color); transform: translateY(-1px); }
        .controls-bar button:active { transform: translateY(0px); }
        .controls-bar button#deleteCoinBtn { background-color: var(--negative-color); }
        .controls-bar button#deleteCoinBtn:hover { opacity: 0.85; background-color: var(--negative-color); }
        .controls-bar button#refreshPriceBtn { background-color: var(--positive-color); padding: 8px 12px; font-size: 1.2em; }
        .controls-bar button#refreshPriceBtn:hover { background-color: #146c43; }
        #apiStatus { flex-grow: 1; text-align: left;
            font-size: 0.9em; /* From File 2 */
            color: var(--text-muted); margin: 0 10px; flex-shrink: 1; min-width: 150px; transition: color 0.3s ease;
        }
        #coinStatus {
            font-size: 0.95em; /* From File 2 */
            color: var(--text-muted); flex-shrink: 0; transition: color 0.3s ease; font-weight: var(--font-weight-normal);
        }
        #themeToggleBtn {
            background: none; border: 1px solid var(--border-color); color: var(--text-muted);
            font-size: 1.3em; /* From File 2 */
            padding: 5px 9px; /* From File 2 */
            line-height: 1; margin-left: 10px; transition: color 0.3s ease, border-color 0.3s ease, background-color 0.2s ease, transform 0.1s ease;
        }
        #themeToggleBtn:hover { background-color: var(--hover-bg); color: var(--text-color); transform: translateY(-1px); }

        .details-content-wrapper { display: grid; grid-template-columns: 0.8fr 1.5fr 1fr;
            gap: 25px; /* From File 2 */
            align-items: flex-start;
        }
        .section {
            background-color: var(--section-bg);
            padding: 25px; /* From File 2 */
            border-radius: 10px; /* From File 2 */
            border: 1px solid var(--border-color);
            box-shadow: 0 3px 10px var(--shadow-color); /* From File 2 */
            transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease; height: 100%; display: flex; flex-direction: column;
        }
        .section > *:last-child { margin-bottom: 0; }

        .form-grid { display: grid; grid-template-columns: 1fr 1fr;
            gap: 20px 15px; /* From File 2 */
            margin-bottom: 25px; /* From File 2 */
        }
        .form-group { display: flex; flex-direction: column; }
        label {
            margin-bottom: 8px; /* From File 2 */
            font-weight: var(--font-weight-semibold); /* From File 2 */
            color: var(--text-muted); font-size: 0.9em; transition: color 0.3s ease;
        }
        input[type="text"], input[type="number"], select {
            padding: 10px 14px; /* From File 2 */
            border: 1px solid var(--input-border);
            border-radius: 6px; /* From File 2 */
            font-size: 1em; box-sizing: border-box; width: 100%; background-color: var(--input-bg); color: var(--text-color);
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.06); /* From File 2 */
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.2s ease, box-shadow 0.2s ease; font-weight: var(--font-weight-normal);
        }
        input[type="number"] { -moz-appearance: textfield; }
        input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button { -webkit-appearance: none; margin: 0; }
        input[type="text"]:focus, input[type="number"]:focus, select:focus { outline: none; border-color: var(--primary-color); box-shadow: 0 0 0 3px var(--focus-ring-color), inset 0 1px 3px rgba(0,0,0,0.06); }
        .output-field, .input-field-read-only {
            padding: 10px 14px; /* From File 2 */
            border: 1px solid var(--border-color); /* From File 2 logic (using main border color) */
            border-radius: 6px; /* From File 2 */
            background-color: var(--output-field-bg); font-weight: var(--font-weight-bold);
            min-height: 42px; /* From File 2 */
            display: flex; align-items: center; color: var(--output-field-text); font-size: 1em; word-break: break-all; box-sizing: border-box; width: 100%; font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace; transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease; direction: ltr; text-align: left;
        }
        .output-field.error { color: var(--negative-color); background-color: rgba(220, 53, 69, 0.05); border-color: rgba(220, 53, 69, 0.3); } /* File 2 error style */

        .market-price-display-group { grid-column: span 2; display: flex; align-items: flex-end;
            gap: 10px; /* From File 2 */
            margin-top: 10px; /* From File 2 */
        }
        .market-price-display-group .form-group { flex-grow: 1; margin-bottom: 0;}
        #marketPriceDisplay {
            font-size: 1.1em; font-weight: var(--font-weight-bold);
            color: var(--primary-color); /* From File 2 */
            background-color: var(--bg-color); /* From File 2 */
        }
        .auto-refresh-group { grid-column: span 2; display: flex; align-items: center;
            gap: 10px; /* From File 2 */
            font-size: 0.95em;
            margin-top: 10px; /* From File 2 */
            background-color: var(--output-field-bg); /* From File 2 */
            padding: 8px 12px; /* From File 2 */
            border-radius: 6px; /* From File 2 */
        }
        .auto-refresh-group input[type="checkbox"] { margin-left: 5px; width: 16px; height: 16px; accent-color: var(--primary-color); }
        .auto-refresh-group label { margin-bottom: 0; font-weight: var(--font-weight-normal); color: var(--text-muted); }

        /* --- Target/Stop Loss Section Styles --- MODIFIED FOR STACKING TARGETS --- */
        #targetsSection {
            margin-top: 30px; /* From File 2 */
            padding-top: 20px; /* From File 2 */
            border-top: 1px dashed var(--border-color); transition: border-color 0.3s ease;
        }
        .targets-grid { /* File 1 custom layout for targets */
            display: grid;
            grid-template-columns: 1fr;
            gap: 18px; /* From File 2, applied as gap between pairs */
        }
        .target-pair { /* File 1 custom layout for targets */
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px; /* Kept File 1's internal gap for pair */
            align-items: end;
        }
        .targets-grid label {
            font-size: 0.85em; text-align: center;
            margin-bottom: 4px; /* From File 2 */
            display: block; font-weight: var(--font-weight-normal);
        }
        .targets-grid .form-group { margin-bottom: 0; }
        .targets-grid input[type="number"] { text-align: center; padding: 8px 10px; }
        .targets-grid .output-field {
            font-size: 1em; /* From File 2 */
            text-align: center;
            padding: 9px 10px; /* From File 2 */
            min-height: 38px; font-weight: var(--font-weight-bold);
        }

        .sl-group {
            margin-top: 15px; padding-top: 15px; border-top: 1px solid var(--border-color);
            display: grid;
            grid-template-columns: 1fr 1fr; /* Preserved from File 1 */
            gap: 18px; /* From File 2 */
        }
        .sl-input input { border-color: var(--negative-color); background-color: rgba(220, 53, 69, 0.05); } /* From File 2 style */
        .sl-input input:focus { border-color: var(--negative-color); box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2); }
        .sl-output { background-color: var(--sl-output-bg) !important; color: var(--sl-output-text) !important; border-color: var(--sl-output-border) !important; font-weight: var(--font-weight-bold); transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease; }


        .section-repurchase { overflow: auto; }
        .repurchase-table {
            width: 100%;
            border-collapse: separate; /* From File 2 */
            border-spacing: 0; /* From File 2 */
            margin-top: 15px;
            font-size: 0.9em; /* From File 2 */
        }
        .repurchase-table th, .repurchase-table td {
            border-bottom: 1px solid var(--border-color); /* From File 2 */
            padding: 8px 6px; /* From File 2 */
            text-align: center;
            white-space: nowrap;
            transition: border-color 0.3s ease, background-color 0.2s ease;
        }
        .repurchase-table th {
            background-color: var(--table-header-bg); font-weight: var(--font-weight-semibold); color: var(--text-color); position: sticky; top: 0; z-index: 1;
            border-top: 1px solid var(--border-color); /* From File 2 */
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        /* Added from File 2 for table cell borders and radii */
        .repurchase-table td:first-child, .repurchase-table th:first-child { border-right: 1px solid var(--border-color); }
        .repurchase-table td:last-child, .repurchase-table th:last-child { border-left: 1px solid var(--border-color); }
        .repurchase-table th:first-child { border-top-right-radius: 6px; }
        .repurchase-table th:last-child { border-top-left-radius: 6px; }
        .repurchase-table tr:last-child td { border-bottom: none; }
        .repurchase-table tr:last-child td:first-child { border-bottom-right-radius: 6px; }
        .repurchase-table tr:last-child td:last-child { border-bottom-left-radius: 6px; }

        .repurchase-table tr:hover td { background-color: var(--hover-bg); }
        .repurchase-table input[type="number"] {
            width: 90%; /* From File 2 */
            padding: 6px 8px; /* From File 2 */
            font-size: 1em; text-align: center; margin: 0 auto; display: block; box-shadow: none;
        }
        .repurchase-table .output-field { font-size: 1em; padding: 6px; min-height: 34px; width: auto; background-color: transparent; border: none; font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace; font-weight: var(--font-weight-bold); color: var(--text-color); text-align: center; }
        .repurchase-table .repurchase-pnl, .repurchase-table .repurchase-pnl-percent { font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace; font-weight: var(--font-weight-bold); direction: ltr; text-align: center; }
        .down-percent {
            font-weight: var(--font-weight-semibold); /* From File 2 */
            min-width: 60px; /* From File 2 */
            display: inline-block; font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace; direction: ltr; padding: 3px 5px; border-radius: 4px; font-size: 0.9em;
        }
        .down-percent.positive { color: var(--positive-color); background-color: rgba(25, 135, 84, 0.1); }
        .down-percent.negative { color: var(--negative-color); background-color: rgba(220, 53, 69, 0.1); }

        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 15px; /* From File 2 */
        }
        .summary-item {
            background-color: var(--bg-color); /* From File 2 */
            padding: 15px; /* From File 2 */
            border-radius: 8px; /* From File 2 */
            border: 1px solid var(--border-color); transition: background-color 0.3s ease, border-color 0.3s ease; display: flex; flex-direction: column;
        }
        .summary-item label {
            font-size: 0.85em; color: var(--text-muted); display: block;
            margin-bottom: 6px; /* From File 2 */
            font-weight: var(--font-weight-normal); transition: color 0.3s ease;
        }
        .summary-item .value {
            font-size: 1.2em; /* From File 2 */
            font-weight: var(--font-weight-bold); color: var(--primary-color); word-wrap: break-word; margin-top: auto; /* Changed from margin-top: 3px */ font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace; transition: color 0.3s ease; direction: ltr; text-align: left;
        }
        .pnl-positive { color: var(--positive-color) !important; }
        .pnl-negative { color: var(--negative-color) !important; }
        .pnl-neutral { color: var(--text-color) !important; }

        @media (max-width: 1200px) { .details-content-wrapper { grid-template-columns: 1fr 1fr; } .details-content-wrapper > .section-current-targets:nth-child(3) { grid-column: 1 / -1; } }
        @media (max-width: 992px) { .summary-table { font-size: 0.85em; } .summary-table th, .summary-table td { padding: 8px 10px; } .summary-totals { grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)); } .details-content-wrapper { grid-template-columns: 1fr; } }
        @media (max-width: 768px) {
            .container { padding: 0 15px; }
            h1 { font-size: 1.6em; margin-bottom: 20px;} h2 { font-size: 1.3em; margin-bottom: 20px;}
            .overall-summary-section { position: static; margin-bottom: 20px; }
            .summary-totals { grid-template-columns: 1fr 1fr; }
            .controls-bar { flex-direction: column; align-items: stretch; gap: 10px; }
            .controls-bar button, .controls-bar select, .controls-bar input { width: 100%; }
            .controls-bar button#deleteCoinBtn { margin-top: 5px; }
            #apiStatus { text-align: center; margin: 8px 0; }
            .details-content-wrapper { gap: 20px; grid-template-columns: 1fr !important; }
            .section { padding: 20px; } .form-grid { grid-template-columns: 1fr; gap: 15px;}
            .market-price-display-group { grid-column: auto; } .auto-refresh-group { grid-column: auto; }
            /* Preserving File 1's responsive target layout */
            .targets-grid { grid-template-columns: 1fr; }
            .target-pair { grid-template-columns: 1fr 1fr; /* Keep pair side-by-side if space allows */ }
            .sl-group { grid-template-columns: 1fr; gap: 12px;} /* File 2 has this gap here too */
        }
        @media (max-width: 480px) {
            body { font-size: 13px; } .container { padding: 0 10px; }
            h1 { font-size: 1.4em; } h2 { font-size: 1.2em; }
            .summary-table { font-size: 0.75em; }
            .summary-table th, .summary-table td { padding: 6px 5px; white-space: normal; }
            .summary-totals { grid-template-columns: 1fr; gap: 10px; }
            .repurchase-table { font-size: 0.8em; }
            .repurchase-table th, .repurchase-table td { padding: 6px 4px; white-space: normal;}
            .summary-grid { grid-template-columns: 1fr; }
            /* Preserving File 1's responsive target layout */
            .target-pair { grid-template-columns: 1fr; /* Stack percent input and price output on very small screens */ }
        }
    </style>
</head>
<body>

<!-- Header, Container, Sections, etc. as before -->
<div class="page-header">
    <strong>تم التصميم بواسطة: المهندس معتز [تداولجي] @TadawulGy</strong> -
    <a href="https://t.me/TadawulGY" target="_blank" rel="noopener noreferrer" class="telegram-link">قناة التليغرام</a> <br>
    نسألكم الدعاء بظهر الغيب
</div>

<div class="container">
    <h1>متابعة الصفقات الشاملة</h1>

    <section class="overall-summary-section">
        <h2>ملخص الصفقات الكلي</h2>
        <div class="summary-table-container">
            <table class="summary-table">
                <thead>
                     <tr>
                         <th>العملة</th><th>الكمية</th><th>المستثمر $</th><th>متوسط الدخول</th>
                         <th>السعر الحالي</th><th>القيمة الحالية $</th><th>الربح/الخسارة $</th><th>الربح/الخسارة %</th>
                     </tr>
                </thead>
                <tbody id="summaryTableBody">
                    <tr><td colspan="8" style="text-align:center; padding: 30px; font-weight: normal; color: var(--text-muted);">أضف عملة أو قم بتحديث الأسعار لعرض الملخص...</td></tr>
                </tbody>
            </table>
        </div>
        <div class="summary-totals">
             <div> <label>إجمالي المبلغ المستثمر</label> <span id="totalInvestedSummary">0.00 $</span> </div>
             <div> <label>إجمالي الربح / الخسارة $</label> <span id="totalPnlAmountSummary">0.00 $</span> </div>
             <div> <label>إجمالي القيمة الحالية</label> <span id="totalCurrentValueSummary">0.00 $</span> </div>
             <div> <label>إجمالي الربح / الخسارة %</label> <span id="totalPnlPercentSummary">0.00 %</span> </div>
        </div>
    </section>

    <div class="controls-bar">
        <label for="coinSelector">العملة النشطة:</label>
        <select id="coinSelector" onchange="handleCoinSelectionChange()"></select>
        <label for="newCoinName">إضافة/بحث:</label>
        <input type="text" id="newCoinName" placeholder="BTCUSDT">
        <button onclick="addOrSwitchCoin()">➕ إضافة/تبديل</button>
        <button id="refreshPriceBtn" title="تحديث كل الأسعار" onclick="fetchAllPrices()">🔄تحديث الاسعار</button>
        <div id="apiStatus">اختر عملة لجلب السعر</div>
        <div id="coinStatus">العملات: 0</div>
        <button id="themeToggleBtn" title="تبديل الوضع">🌙</button>
        
<input type="file" id="importTxtFile" accept=".txt" style="display:none" onchange="handleImportTxtFile(event)">
<button onclick="document.getElementById('importTxtFile').click()">استيراد TXT</button>
<button onclick="downloadAllDataAsTxt()">حفظ كـ TXT</button>

<button id="deleteCoinBtn" onclick="deleteCurrentCoin()" disabled title="حذف العملة المحددة">🗑️ حذف</button>
    </div>

    <div class="details-content-wrapper">
        <section class="section section-initial">
            <h2>تفاصيل الدخول (<span id="currentCoinDisplay1">---</span>)</h2>
            <div class="form-grid initial-purchase-grid">
                <div class="form-group">
                    <label for="initialEntryPrice">سعر الدخول الأولي:</label>
                    <input type="number" id="initialEntryPrice" step="any" placeholder="0.00" oninput="saveAndCalculate()">
                </div>
                <div class="form-group">
                    <label for="initialAmountDollars">المبلغ بالدولار $:</label>
                    <input type="number" id="initialAmountDollars" step="any" placeholder="0.00" oninput="saveAndCalculate()">
                </div>
                 <div class="form-group" style="grid-column: span 2;">
                     <label>كمية العملة المحصلة:</label>
                     <div id="initialCoinQty" class="output-field">0.00</div>
                 </div>
                 <div class="market-price-display-group" style="grid-column: span 2;">
                     <div class="form-group">
                         <label>السعر الحالي (للعملة النشطة):</label>
                         <div id="marketPriceDisplay" class="output-field">---</div>
                     </div>
                 </div>
                 <div class="auto-refresh-group" style="grid-column: span 2;">
                     <input type="checkbox" id="autoRefreshCheckbox">
                     <label for="autoRefreshCheckbox">تحديث تلقائي للأسعار (كل 30 ثانية)</label>
                 </div>
            </div>
        </section>

        <section class="section section-repurchase">
            <h2>التعزيز DCA (<span id="currentCoinDisplay2">---</span>)</h2>
            <div style="overflow-x: auto;">
                <table class="repurchase-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>هبوط %</th>
                            <th>سعر التعزيز</th>
                            <th>المبلغ $</th>
                            <th>الكمية</th>
                            <th>ربح/خسارة الصفقة $</th>
                            <th>ربح/خسارة الصفقة %</th>
                        </tr>
                    </thead>
                    <tbody id="repurchaseRows"></tbody>
                </table>
            </div>
        </section>

        <section class="section section-current-targets">
            <h2>الوضع الحالي والأهداف (<span id="currentCoinDisplay3">---</span>)</h2>
            <div class="summary-grid">
                <div class="summary-item"> <label>إجمالي الكمية:</label> <div id="totalCoinQty" class="value">0.00</div> </div>
                <div class="summary-item"> <label>إجمالي المستثمر $:</label> <div id="totalInvestedAmount" class="value">0.00</div> </div>
                <div class="summary-item"> <label>متوسط السعر:</label> <div id="averageEntryPrice" class="value">0.00</div> </div>
                <div class="summary-item"> <label>القيمة الحالية $:</label> <div id="currentPortfolioValue" class="value">0.00</div> </div>
                <div class="summary-item"> <label>الربح/الخسارة $:</label> <div id="pnlAmount" class="value pnl-neutral">0.00</div> </div>
                <div class="summary-item"> <label>الربح/الخسارة %:</label> <div id="pnlPercent" class="value pnl-neutral">0.00%</div> </div>
            </div>

            <div id="targetsSection">
                 <h3>الأهداف ووقف الخسارة (من متوسط السعر)</h3>
                 <div class="targets-grid">
                    <!-- Target 1 -->
                    <div class="target-pair">
                        <div class="form-group"> <label for="tpPercent1">هدف 1 (%)</label> <input type="number" id="tpPercent1" step="any" min="0" placeholder="%" oninput="saveAndCalculate()"> </div>
                        <div class="form-group"> <label>سعر خروج 1</label> <div id="tpPrice1" class="output-field">0.00</div> </div>
                    </div>
                    <!-- Target 2 -->
                    <div class="target-pair">
                        <div class="form-group"> <label for="tpPercent2">هدف 2 (%)</label> <input type="number" id="tpPercent2" step="any" min="0" placeholder="%" oninput="saveAndCalculate()"> </div>
                        <div class="form-group"> <label>سعر خروج 2</label> <div id="tpPrice2" class="output-field">0.00</div> </div>
                    </div>
                    <!-- Target 3 -->
                    <div class="target-pair">
                        <div class="form-group"> <label for="tpPercent3">هدف 3 (%)</label> <input type="number" id="tpPercent3" step="any" min="0" placeholder="%" oninput="saveAndCalculate()"> </div>
                        <div class="form-group"> <label>سعر خروج 3</label> <div id="tpPrice3" class="output-field">0.00</div> </div>
                    </div>
                    <!-- Stop Loss -->
                    <div class="sl-group"> <!-- SL group still spans full width of its parent grid context -->
                        <div class="form-group sl-input">
                            <label for="slPercent">وقف خسارة (%)</label>
                            <input type="number" id="slPercent" step="any" min="0" placeholder="%" oninput="saveAndCalculate()">
                        </div>
                        <div class="form-group">
                            <label>سعر وقف الخسارة</label>
                            <div id="slPrice" class="output-field sl-output">0.00</div>
                        </div>
                    </div>
                 </div>
            </div>
        </section>
    </div>
</div>

<script>
    // --- المتغيرات العامة ---
    const repurchaseTableBody = document.getElementById('repurchaseRows');
    const marketPriceDisplay = document.getElementById('marketPriceDisplay');
    const apiStatusDiv = document.getElementById('apiStatus');
    const autoRefreshCheckbox = document.getElementById('autoRefreshCheckbox');
    const coinSelector = document.getElementById('coinSelector');
    const newCoinNameInput = document.getElementById('newCoinName');
    const coinStatusDiv = document.getElementById('coinStatus');
    const summaryTableBody = document.getElementById('summaryTableBody');
    const totalInvestedSummaryEl = document.getElementById('totalInvestedSummary');
    const totalPnlAmountSummaryEl = document.getElementById('totalPnlAmountSummary');
    const totalCurrentValueSummaryEl = document.getElementById('totalCurrentValueSummary');
    const totalPnlPercentSummaryEl = document.getElementById('totalPnlPercentSummary');
    const themeToggleBtn = document.getElementById('themeToggleBtn');
    const currentCoinDisplayElements = [
        document.getElementById('currentCoinDisplay1'),
        document.getElementById('currentCoinDisplay2'),
        document.getElementById('currentCoinDisplay3')
    ];

    const maxRepurchaseEntries = 10;
    let fetchTimeout, autoRefreshIntervalId = null;
    const AUTO_REFRESH_INTERVAL = 30000;
    const LS_KEY_DATA = 'cryptoTrackerUniversal_v9_data'; // Incremented version
    const LS_KEY_THEME = 'cryptoTrackerUniversal_v9_theme';
    let allCoinData = {};
    let currentMarketPrices = {};
    let activeCoinSymbol = null;

    // --- تهيئة هيكل بيانات العملة الافتراضي ---
    function getDefaultCoinDataStructure() {
        const repurchases = Array.from({ length: maxRepurchaseEntries }, () => ({ price: '', amount: '' }));
        return {
            initialEntryPrice: '', initialAmountDollars: '',
            repurchases: repurchases,
            targets: { tp1: '', tp2: '', tp3: '', sl: '' }
        };
     }

    // --- حفظ بيانات العملات ---
    function saveAllDataToLocalStorage() {
        if (activeCoinSymbol && allCoinData[activeCoinSymbol]) {
            updateActiveCoinDataInMemory();
        }
        const dataToSave = { coins: allCoinData, active: activeCoinSymbol };
        try {
            localStorage.setItem(LS_KEY_DATA, JSON.stringify(dataToSave));
        } catch (error) {
            console.error("Error saving coin data:", error);
            apiStatusDiv.textContent = "خطأ في حفظ البيانات!";
            apiStatusDiv.style.color = 'var(--negative-color)';
        }
     }

    // --- تحميل بيانات العملات ---
    function loadAllDataFromLocalStorage() {
        const savedData = localStorage.getItem(LS_KEY_DATA);
        if (savedData) {
            try {
                const parsedData = JSON.parse(savedData);
                if (parsedData && typeof parsedData.coins === 'object' && parsedData.coins !== null) {
                    allCoinData = parsedData.coins;
                    activeCoinSymbol = parsedData.active || null;
                    Object.keys(allCoinData).forEach(symbol => {
                        if (!allCoinData[symbol]) {
                           allCoinData[symbol] = getDefaultCoinDataStructure();
                        }
                        if (!allCoinData[symbol].repurchases || allCoinData[symbol].repurchases.length !== maxRepurchaseEntries) {
                            const existingRepurchases = allCoinData[symbol].repurchases || [];
                            allCoinData[symbol].repurchases = Array.from({ length: maxRepurchaseEntries }, (_, i) =>
                                existingRepurchases[i] || { price: '', amount: '' }
                            );
                        }
                        if (!allCoinData[symbol].targets) {
                             allCoinData[symbol].targets = { tp1: '', tp2: '', tp3: '', sl: '' };
                        }
                        currentMarketPrices[symbol] = null;
                    });
                } else {
                    allCoinData = {}; activeCoinSymbol = null; currentMarketPrices = {};
                }
                return true;
            } catch (error) {
                console.error("Error loading or parsing coin data:", error);
                allCoinData = {}; activeCoinSymbol = null; currentMarketPrices = {};
                localStorage.removeItem(LS_KEY_DATA);
                return false;
            }
        }
        allCoinData = {}; activeCoinSymbol = null; currentMarketPrices = {};
        return false;
     }

     // --- Theme Management ---
    function applyTheme(theme) {
        if (theme === 'dark') {
            document.body.classList.add('dark-mode');
            themeToggleBtn.textContent = '☀️';
            themeToggleBtn.title = 'الوضع النهاري';
        } else {
            document.body.classList.remove('dark-mode');
            themeToggleBtn.textContent = '🌙';
            themeToggleBtn.title = 'الوضع الليلي';
        }
    }
    function toggleTheme() {
        const currentTheme = document.body.classList.contains('dark-mode') ? 'light' : 'dark';
        applyTheme(currentTheme);
        localStorage.setItem(LS_KEY_THEME, currentTheme);
    }
    function loadThemePreference() {
        const preferredTheme = localStorage.getItem(LS_KEY_THEME) || 'light'; // Default to light
        applyTheme(preferredTheme);
    }

    // --- تحديث القائمة المنسدلة للعملات ---
    function updateCoinSelector() {
        const previouslySelected = coinSelector.value;
        coinSelector.innerHTML = '<option value="">-- اختر عملة --</option>';
        const coinSymbols = Object.keys(allCoinData).sort();
        coinSymbols.forEach(symbol => {
            const option = document.createElement('option');
            option.value = symbol; option.textContent = symbol;
            coinSelector.appendChild(option);
        });
        if (activeCoinSymbol && allCoinData[activeCoinSymbol]) {
            coinSelector.value = activeCoinSymbol;
        } else if (previouslySelected && allCoinData[previouslySelected]) {
            coinSelector.value = previouslySelected; activeCoinSymbol = previouslySelected;
        } else if (coinSymbols.length > 0) {
            coinSelector.value = coinSymbols[0]; activeCoinSymbol = coinSymbols[0];
        } else {
            activeCoinSymbol = null; clearUIFields();
        }
        if (coinSelector.value) {
            displayCoinData(coinSelector.value);
        } else {
             updateCurrentCoinDisplay("لا عملة محددة");
        }
        updateCoinStatus();
    }

    // --- عرض بيانات عملة محددة في الواجهة ---
    function displayCoinData(symbol) {
        activeCoinSymbol = symbol;
        const data = allCoinData[symbol];
        if (!data) {
            console.warn(`No data for symbol: ${symbol}. Clearing UI.`);
            clearUIFields(); updateCurrentCoinDisplay(symbol || "خطأ"); calculateActiveCoinDetails(); return;
        }
        updateCurrentCoinDisplay(symbol);
        document.getElementById('initialEntryPrice').value = data.initialEntryPrice || '';
        document.getElementById('initialAmountDollars').value = data.initialAmountDollars || '';
        if (data.repurchases && data.repurchases.length === maxRepurchaseEntries) {
            for (let i = 0; i < maxRepurchaseEntries; i++) {
                const priceInput = document.getElementById(`repurchasePrice${i + 1}`);
                const amountInput = document.getElementById(`repurchaseAmount${i + 1}`);
                if (priceInput) priceInput.value = data.repurchases[i]?.price || '';
                if (amountInput) amountInput.value = data.repurchases[i]?.amount || '';
            }
        } else {
             for (let i = 1; i <= maxRepurchaseEntries; i++) {
                 const pIn = document.getElementById(`repurchasePrice${i}`); if(pIn) pIn.value = '';
                 const aIn = document.getElementById(`repurchaseAmount${i}`); if(aIn) aIn.value = '';
             }
         }
        if (data.targets) {
            document.getElementById('tpPercent1').value = data.targets.tp1 || '';
            document.getElementById('tpPercent2').value = data.targets.tp2 || '';
            document.getElementById('tpPercent3').value = data.targets.tp3 || '';
            document.getElementById('slPercent').value = data.targets.sl || '';
        } else {
             document.getElementById('tpPercent1').value = ''; document.getElementById('tpPercent2').value = '';
             document.getElementById('tpPercent3').value = ''; document.getElementById('slPercent').value = '';
        }
        const activeCoinPrice = currentMarketPrices[symbol];
        if (activeCoinPrice !== null && activeCoinPrice !== undefined && !isNaN(activeCoinPrice)) {
            marketPriceDisplay.textContent = formatNumber(activeCoinPrice, guessDecimalPlaces(activeCoinPrice));
            marketPriceDisplay.classList.remove('error');
        } else {
            marketPriceDisplay.textContent = '---'; marketPriceDisplay.classList.add('error');
        }
        calculateActiveCoinDetails(); updateCoinStatus();
    }

    // --- مسح جميع حقول الإدخال والإخراج في الواجهة ---
    function clearUIFields() {
        document.getElementById('initialEntryPrice').value = '';
        document.getElementById('initialAmountDollars').value = '';
        document.getElementById('tpPercent1').value = ''; document.getElementById('tpPercent2').value = '';
        document.getElementById('tpPercent3').value = ''; document.getElementById('slPercent').value = '';
        for (let i = 1; i <= maxRepurchaseEntries; i++) {
            const pIn = document.getElementById(`repurchasePrice${i}`); if(pIn) pIn.value = '';
            const aIn = document.getElementById(`repurchaseAmount${i}`); if(aIn) aIn.value = '';
        }
        marketPriceDisplay.textContent = '---'; marketPriceDisplay.classList.remove('error');
        apiStatusDiv.textContent = 'اختر عملة أو أضف واحدة جديدة'; apiStatusDiv.style.color = 'var(--text-muted)';
        updateCurrentCoinDisplay("لا عملة محددة"); calculateActiveCoinDetails();
    }

    // --- تحديث اسم العملة المعروض في العناوين ---
    function updateCurrentCoinDisplay(symbol) {
        const displayText = symbol || "---";
        currentCoinDisplayElements.forEach(el => { if (el) el.textContent = displayText; });
    }

     // --- تحديث رسالة حالة العملة (عدد العملات) ---
     function updateCoinStatus() {
         const count = Object.keys(allCoinData).length;
         coinStatusDiv.textContent = `العملات: ${count}`;
         document.getElementById('deleteCoinBtn').disabled = !activeCoinSymbol;
     }

    // --- قراءة البيانات الحالية من الواجهة وتحديثها في كائن allCoinData (في الذاكرة) ---
    function updateActiveCoinDataInMemory() {
        if (!activeCoinSymbol || !allCoinData[activeCoinSymbol]) return;
        const currentData = allCoinData[activeCoinSymbol];
        currentData.initialEntryPrice = document.getElementById('initialEntryPrice').value;
        currentData.initialAmountDollars = document.getElementById('initialAmountDollars').value;
        currentData.repurchases = [];
        for (let i = 1; i <= maxRepurchaseEntries; i++) {
            currentData.repurchases.push({
                price: document.getElementById(`repurchasePrice${i}`)?.value || '',
                amount: document.getElementById(`repurchaseAmount${i}`)?.value || ''
            });
        }
        currentData.targets = {
            tp1: document.getElementById('tpPercent1').value, tp2: document.getElementById('tpPercent2').value,
            tp3: document.getElementById('tpPercent3').value, sl: document.getElementById('slPercent').value
        };
    }

    // --- دالة الحفظ والحساب (للعملة النشطة + تحديث الملخص) ---
    function saveAndCalculate() {
        calculateActiveCoinDetails();
        if (activeCoinSymbol) {
             updateActiveCoinDataInMemory(); saveAllDataToLocalStorage();
        }
        updateSummaryTable();
    }

    // --- إنشاء صفوف التعزيز ---
    function createRepurchaseRows() {
        repurchaseTableBody.innerHTML = '';
        for (let i = 1; i <= maxRepurchaseEntries; i++) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${i}</td>
                <td><span class="down-percent" id="downPercent${i}">-%</span></td>
                <td><input type="number" id="repurchasePrice${i}" step="any" placeholder="السعر" oninput="saveAndCalculate()"></td>
                <td><input type="number" id="repurchaseAmount${i}" step="any" placeholder="$" oninput="saveAndCalculate()"></td>
                <td><div class="output-field" id="repurchaseQty${i}">0.00</div></td>
                <td><div class="output-field repurchase-pnl" id="repurchasePnl${i}">0.00</div></td>
                <td><div class="output-field repurchase-pnl-percent" id="repurchasePnlPercent${i}">0.00%</div></td>
            `;
            repurchaseTableBody.appendChild(row);
        }
    }

    // --- جلب سعر **واحد** لعملة محددة ---
    async function fetchSinglePrice(symbol) {
        if (!symbol || typeof symbol !== 'string' || symbol.length < 4) {
             return { symbol: symbol, price: null, source: null, error: 'رمز غير صالح' };
        }
        const binanceApiUrl = `https://api.binance.com/api/v3/ticker/price?symbol=${symbol}`;
        const kucoinSymbol = symbol.endsWith('USDT') ? `${symbol.slice(0, -4)}-USDT` : symbol;
        const kucoinApiUrl = `https://api.kucoin.com/api/v1/market/orderbook/level1?symbol=${kucoinSymbol}`;
        let price = null; let source = ''; let error = null;
        try {
            const response = await fetch(binanceApiUrl);
            if (response.ok) {
                 const data = await response.json();
                 if (data && data.price && !isNaN(parseFloat(data.price))) {
                     price = parseFloat(data.price); source = 'Binance';
                 }
             }
        } catch (e) { console.warn(`Binance fetch failed for ${symbol}:`, e.message); }
        if (price === null) {
            try {
                const kucoinResponse = await fetch(kucoinApiUrl);
                 if (kucoinResponse.ok) {
                    const kucoinData = await kucoinResponse.json();
                    if (kucoinData.code === '200000' && kucoinData.data && kucoinData.data.price && !isNaN(parseFloat(kucoinData.data.price))) {
                        price = parseFloat(kucoinData.data.price); source = 'KuCoin';
                    }
                 }
            } catch (e) { console.warn(`KuCoin fetch failed for ${kucoinSymbol}:`, e.message); }
        }
        if (price !== null) {
            return { symbol: symbol, price: price, source: source, error: null };
        } else {
             error = 'فشل جلب السعر';
             console.error(`Failed to fetch price for ${symbol} from both Binance and KuCoin.`);
            return { symbol: symbol, price: null, source: null, error: error };
        }
     }

    // --- جلب أسعار **كل** العملات المراقبة ---
    async function fetchAllPrices(isAutoRefresh = false) {
        const trackedSymbols = Object.keys(allCoinData);
        if (trackedSymbols.length === 0) {
            if (!isAutoRefresh) {
                apiStatusDiv.textContent = 'لا توجد عملات للمراقبة.'; apiStatusDiv.style.color = 'var(--text-muted)';
                summaryTableBody.innerHTML = '<tr><td colspan="8" style="text-align:center; padding: 30px; font-weight: normal; color: var(--text-muted);">أضف عملة للبدء.</td></tr>';
                resetTotals();
            } return;
        }
        if (!isAutoRefresh) {
            apiStatusDiv.textContent = `جاري جلب أسعار ${trackedSymbols.length} عملة...`; apiStatusDiv.style.color = 'var(--primary-color)';
            summaryTableBody.innerHTML = '<tr><td colspan="8" style="text-align:center; padding: 30px; font-weight: normal; color: var(--text-muted);">جاري تحديث الأسعار...</td></tr>';
        }
        const pricePromises = trackedSymbols.map(symbol => fetchSinglePrice(symbol));
        try {
            const results = await Promise.allSettled(pricePromises);
            let successCount = 0; let failCount = 0; const fetchTimestamp = new Date();
            results.forEach(result => {
                 if (result.status === 'fulfilled') {
                    const { symbol, price, error } = result.value;
                    if (error === null && price !== null) {
                        currentMarketPrices[symbol] = price; successCount++;
                    } else {
                        if (currentMarketPrices[symbol] === undefined) { currentMarketPrices[symbol] = null; }
                        failCount++; console.error(`Price fetch logic error for ${symbol}: ${error || 'No price'}`);
                    }
                 } else {
                     failCount++; console.error(`Promise rejected for symbol fetch:`, result.reason);
                      const failedSymbol = trackedSymbols.find(s => result.reason?.message?.includes(s));
                      if (failedSymbol && currentMarketPrices[failedSymbol] === undefined) {
                          currentMarketPrices[failedSymbol] = null;
                      }
                 }
            });
            if (activeCoinSymbol) {
                 const price = currentMarketPrices[activeCoinSymbol];
                 if (price !== null && price !== undefined && !isNaN(price)) {
                     marketPriceDisplay.textContent = formatNumber(price, guessDecimalPlaces(price)); marketPriceDisplay.classList.remove('error');
                 } else {
                     marketPriceDisplay.textContent = "فشل الجلب"; marketPriceDisplay.classList.add('error');
                 }
            }
            calculateActiveCoinDetails(); updateSummaryTable();
            const statusMsg = `${successCount} ناجح, ${failCount} فشل (${fetchTimestamp.toLocaleTimeString('ar-EG')})`;
            apiStatusDiv.textContent = isAutoRefresh ? `تلقائي: ${statusMsg}` : `اكتمل: ${statusMsg}`;
            apiStatusDiv.style.color = failCount > 0 ? 'var(--negative-color)' : 'var(--positive-color)';
            if (autoRefreshCheckbox.checked && !autoRefreshIntervalId) { startAutoRefresh(); }
        } catch (error) {
            console.error("Unexpected error during fetchAllPrices:", error);
            if (!isAutoRefresh) {
                apiStatusDiv.textContent = `خطأ عام أثناء تحديث الأسعار.`; apiStatusDiv.style.color = 'var(--negative-color)';
            }
            stopAutoRefresh(); updateSummaryTable();
        }
    }

    // --- حساب بيانات الملخص لكل العملات ---
    function calculateSummaryData() {
        const summaryData = []; const coinSymbols = Object.keys(allCoinData);
        let grandTotalInvested = 0; let grandTotalPnlAmount = 0;
        coinSymbols.forEach(symbol => {
            const data = allCoinData[symbol]; if (!data) return;
            const marketPrice = currentMarketPrices[symbol];
            const initialEntryPrice = parseFloat(data.initialEntryPrice) || 0;
            const initialAmountDollars = parseFloat(data.initialAmountDollars) || 0;
            let totalCoinQty = 0; let totalInvestedAmount = 0; let errorMsg = null;
            if (initialEntryPrice > 0 && initialAmountDollars > 0) {
                totalCoinQty = initialAmountDollars / initialEntryPrice; totalInvestedAmount = initialAmountDollars;
            } else if (initialAmountDollars > 0 && initialEntryPrice <= 0) { errorMsg = "سعر الدخول الأولي مفقود"; }
            if (data.repurchases) {
                data.repurchases.forEach(rp => {
                    const rpPrice = parseFloat(rp.price) || 0; const rpAmount = parseFloat(rp.amount) || 0;
                    if (rpPrice > 0 && rpAmount > 0) {
                        totalCoinQty += rpAmount / rpPrice; totalInvestedAmount += rpAmount;
                    } else if (rpAmount > 0 && rpPrice <=0) { if (!errorMsg) errorMsg = "سعر تعزيز مفقود"; }
                });
            }
            const averageEntryPrice = totalCoinQty > 0 ? totalInvestedAmount / totalCoinQty : 0;
            let currentPortfolioValue = 0; let pnlAmount = 0; let pnlPercent = 0;
            if (marketPrice === null || marketPrice === undefined) { if (!errorMsg) errorMsg = "لم يتم جلب السعر"; }
            else if (totalInvestedAmount <= 0 && totalCoinQty <= 0) { pnlAmount = 0; pnlPercent = 0; currentPortfolioValue = 0; }
            else if (errorMsg) { pnlAmount = NaN; pnlPercent = NaN; currentPortfolioValue = NaN; }
            else {
                currentPortfolioValue = totalCoinQty * marketPrice;
                pnlAmount = currentPortfolioValue - totalInvestedAmount;
                pnlPercent = totalInvestedAmount > 0 ? (pnlAmount / totalInvestedAmount) * 100 : 0;
            }
            if (!isNaN(pnlAmount) && totalInvestedAmount >= 0) {
                grandTotalInvested += totalInvestedAmount; grandTotalPnlAmount += pnlAmount;
            }
            summaryData.push({
                symbol, totalCoinQty, totalInvestedAmount, averageEntryPrice, marketPrice,
                currentPortfolioValue, pnlAmount, pnlPercent, error: errorMsg
            });
        });
        summaryData.sort((a, b) => a.symbol.localeCompare(b.symbol));
        const grandTotalCurrentValue = grandTotalInvested + grandTotalPnlAmount;
        const grandTotalPnlPercent = grandTotalInvested > 0 ? (grandTotalPnlAmount / grandTotalInvested) * 100 : 0;
        return {
            summaryRows: summaryData,
            totals: { invested: grandTotalInvested, pnlAmount: grandTotalPnlAmount, currentValue: grandTotalCurrentValue, pnlPercent: grandTotalPnlPercent }
        };
    }

    // --- تحديث جدول الملخص + الإجماليات ---
    function updateSummaryTable() {
        const { summaryRows, totals } = calculateSummaryData();
        summaryTableBody.innerHTML = '';
        if (summaryRows.length === 0) {
            summaryTableBody.innerHTML = '<tr><td colspan="8" style="text-align:center; padding: 30px; font-weight: normal; color: var(--text-muted);">لا توجد عملات مضافة حالياً.</td></tr>';
            resetTotals(); return;
        }
        summaryRows.forEach(item => {
            const row = document.createElement('tr');
            const pnlAmountValid = !isNaN(item.pnlAmount); const pnlPercentValid = !isNaN(item.pnlPercent);
            const marketPriceValid = item.marketPrice !== null && !isNaN(item.marketPrice);
            const avgPriceValid = !isNaN(item.averageEntryPrice) && item.averageEntryPrice > 0;
            const portfolioValueValid = !isNaN(item.currentPortfolioValue);
            const pnlAmountClass = pnlAmountValid ? (item.pnlAmount > 0 ? 'pnl-positive' : (item.pnlAmount < 0 ? 'pnl-negative' : '')) : '';
            const pnlPercentClass = pnlPercentValid ? (item.pnlPercent > 0 ? 'pnl-positive' : (item.pnlPercent < 0 ? 'pnl-negative' : '')) : '';
            const displayPrice = marketPriceValid ? formatNumber(item.marketPrice, guessDecimalPlaces(item.marketPrice)) : `<span class="error">${item.error || 'لا يوجد سعر'}</span>`;
            const displayAvgPrice = avgPriceValid ? formatNumber(item.averageEntryPrice, guessDecimalPlaces(item.averageEntryPrice)) : (item.totalInvestedAmount > 0 ? '<span class="error">خطأ</span>' : '0.00');
            const displayPortfolioValue = marketPriceValid && portfolioValueValid ? formatNumber(item.currentPortfolioValue, 2) : (item.error ? '-' : '0.00');
            const displayPnlAmount = marketPriceValid && pnlAmountValid ? formatNumber(item.pnlAmount, 2) : (item.error ? '-' : '0.00');
            const displayPnlPercent = marketPriceValid && pnlPercentValid && item.totalInvestedAmount > 0 ? `${formatNumber(item.pnlPercent, 2)}%` : (item.error ? '-' : '0.00%');
            const displayQuantity = !isNaN(item.totalCoinQty) ? formatNumber(item.totalCoinQty, 4) : '<span class="error">خطأ</span>';
            const displayInvested = !isNaN(item.totalInvestedAmount) ? formatNumber(item.totalInvestedAmount, 2) : '<span class="error">خطأ</span>';
            row.innerHTML = `
                <td class="coin-symbol summary-coin-clickable" data-symbol="${item.symbol}" title="تنشيط عملة ${item.symbol}">${item.symbol}</td> <td class="number-col">${displayQuantity}</td>
                <td class="number-col">${displayInvested}</td> <td class="number-col">${displayAvgPrice}</td>
                <td class="number-col">${displayPrice}</td> <td class="number-col">${displayPortfolioValue}</td>
                <td class="number-col ${pnlAmountClass}">${displayPnlAmount}</td> <td class="number-col ${pnlPercentClass}">${displayPnlPercent}</td>
            `;
            summaryTableBody.appendChild(row);
        });
        totalInvestedSummaryEl.textContent = `${formatNumber(totals.invested, 2)} $`;
        totalPnlAmountSummaryEl.textContent = `${formatNumber(totals.pnlAmount, 2)} $`;
        totalCurrentValueSummaryEl.textContent = `${formatNumber(totals.currentValue, 2)} $`;
        totalPnlPercentSummaryEl.textContent = `${formatNumber(totals.pnlPercent, 2)} %`;
        totalPnlAmountSummaryEl.className = totals.pnlAmount > 0 ? 'totals-positive' : (totals.pnlAmount < 0 ? 'totals-negative' : '');
        totalPnlPercentSummaryEl.className = totals.pnlPercent > 0 ? 'totals-positive' : (totals.pnlPercent < 0 ? 'totals-negative' : '');
        totalCurrentValueSummaryEl.className = totals.pnlAmount >= 0 ? 'totals-positive' : 'totals-negative';
    }

     // --- Reset Totals Display ---
     function resetTotals() {
         totalInvestedSummaryEl.textContent = `0.00 $`; totalPnlAmountSummaryEl.textContent = `0.00 $`;
         totalCurrentValueSummaryEl.textContent = `0.00 $`; totalPnlPercentSummaryEl.textContent = `0.00 %`;
         totalPnlAmountSummaryEl.className = ''; totalPnlPercentSummaryEl.className = ''; totalCurrentValueSummaryEl.className = '';
     }

    // --- الدالة الرئيسية لحساب تفاصيل العملة النشطة ---
    function calculateActiveCoinDetails() {
        document.getElementById('initialCoinQty').textContent = formatNumber(0, 8);
        document.getElementById('totalCoinQty').textContent = formatNumber(0, 8);
        document.getElementById('totalInvestedAmount').textContent = formatNumber(0, 2);
        document.getElementById('averageEntryPrice').textContent = formatNumber(0, 8);
        document.getElementById('currentPortfolioValue').textContent = formatNumber(0, 2);
        const pnlAmountElement = document.getElementById('pnlAmount');
        const pnlPercentElement = document.getElementById('pnlPercent');
        pnlAmountElement.textContent = formatNumber(0, 2); pnlPercentElement.textContent = formatNumber(0, 2) + '%';
        pnlAmountElement.className = 'value pnl-neutral'; pnlPercentElement.className = 'value pnl-neutral';
        document.getElementById('tpPrice1').textContent = formatNumber(0, 8);
        document.getElementById('tpPrice2').textContent = formatNumber(0, 8);
        document.getElementById('tpPrice3').textContent = formatNumber(0, 8);
        document.getElementById('slPrice').textContent = formatNumber(0, 8);

        for (let i = 1; i <= maxRepurchaseEntries; i++) {
             const dpSpan = document.getElementById(`downPercent${i}`);
             const rpQtyDiv = document.getElementById(`repurchaseQty${i}`);
             const rpPnlDiv = document.getElementById(`repurchasePnl${i}`);
             const rpPnlPercentDiv = document.getElementById(`repurchasePnlPercent${i}`);
             if (dpSpan) { dpSpan.textContent = '-%'; dpSpan.className = 'down-percent'; }
             if (rpQtyDiv) rpQtyDiv.textContent = formatNumber(0, 8);
             if (rpPnlDiv) { rpPnlDiv.textContent = formatNumber(0, 2); rpPnlDiv.className = 'output-field repurchase-pnl'; }
             if (rpPnlPercentDiv) { rpPnlPercentDiv.textContent = formatNumber(0, 2) + '%'; rpPnlPercentDiv.className = 'output-field repurchase-pnl-percent'; }
        }
        if (!activeCoinSymbol || !allCoinData[activeCoinSymbol]) return;

        const data = allCoinData[activeCoinSymbol];
        const marketPrice = currentMarketPrices[activeCoinSymbol] || 0;
        const initialEntryPrice = parseFloat(data.initialEntryPrice) || 0;
        const initialAmountDollars = parseFloat(data.initialAmountDollars) || 0;
        const initialCoinQty = initialEntryPrice > 0 ? initialAmountDollars / initialEntryPrice : 0;
        document.getElementById('initialCoinQty').textContent = formatNumber(initialCoinQty, 8);
        let totalCoinQty = initialCoinQty; let totalInvestedAmount = initialAmountDollars;

        for (let i = 1; i <= maxRepurchaseEntries; i++) {
            const rpPriceInput = document.getElementById(`repurchasePrice${i}`);
            const rpAmountInput = document.getElementById(`repurchaseAmount${i}`);
            const repurchasePrice = parseFloat(rpPriceInput?.value) || 0;
            const repurchaseAmount = parseFloat(rpAmountInput?.value) || 0;
            let changePercent = 0; let repurchaseQty = 0;
            let pnlForThisRepurchase = 0; let pnlPercentForThisRepurchase = 0;

            if (repurchasePrice > 0 && repurchaseAmount > 0) {
                if (initialEntryPrice > 0) {
                    changePercent = ((repurchasePrice - initialEntryPrice) / initialEntryPrice) * 100;
                }
                repurchaseQty = repurchaseAmount / repurchasePrice;
                totalCoinQty += repurchaseQty; totalInvestedAmount += repurchaseAmount;
                if (marketPrice > 0) {
                     const currentValueOfRepurchase = repurchaseQty * marketPrice;
                     pnlForThisRepurchase = currentValueOfRepurchase - repurchaseAmount;
                     pnlPercentForThisRepurchase = repurchaseAmount > 0 ? (pnlForThisRepurchase / repurchaseAmount) * 100 : 0;
                 }
            }
            const dpSpan = document.getElementById(`downPercent${i}`);
            const rpQtyDiv = document.getElementById(`repurchaseQty${i}`);
            const rpPnlDiv = document.getElementById(`repurchasePnl${i}`);
            const rpPnlPercentDiv = document.getElementById(`repurchasePnlPercent${i}`);

            if (dpSpan) {
                dpSpan.textContent = (changePercent !== 0 && isFinite(changePercent)) ? `${formatNumber(changePercent, 2)}%` : '-%';
                dpSpan.className = 'down-percent';
                if (changePercent < 0) dpSpan.classList.add('negative'); else if (changePercent > 0) dpSpan.classList.add('positive');
            }
            if (rpQtyDiv) rpQtyDiv.textContent = formatNumber(repurchaseQty, 8);
            if (rpPnlDiv) {
                 rpPnlDiv.textContent = formatNumber(pnlForThisRepurchase, 2);
                 rpPnlDiv.className = 'output-field repurchase-pnl';
                 if (pnlForThisRepurchase > 0) rpPnlDiv.classList.add('pnl-positive');
                 else if (pnlForThisRepurchase < 0) rpPnlDiv.classList.add('pnl-negative');
            }
            if (rpPnlPercentDiv) {
                 rpPnlPercentDiv.textContent = `${formatNumber(pnlPercentForThisRepurchase, 2)}%`;
                 rpPnlPercentDiv.className = 'output-field repurchase-pnl-percent';
                 if (pnlPercentForThisRepurchase > 0) rpPnlPercentDiv.classList.add('pnl-positive');
                 else if (pnlPercentForThisRepurchase < 0) rpPnlPercentDiv.classList.add('pnl-negative');
            }
        }

        const averageEntryPrice = totalCoinQty > 0 ? totalInvestedAmount / totalCoinQty : 0;
        const currentPortfolioValue = totalCoinQty * marketPrice;
        const pnlAmount = (totalInvestedAmount > 0 || currentPortfolioValue > 0 || totalCoinQty > 0) ? currentPortfolioValue - totalInvestedAmount : 0;
        const pnlPercent = totalInvestedAmount > 0 ? (pnlAmount / totalInvestedAmount) * 100 : 0;
        document.getElementById('totalCoinQty').textContent = formatNumber(totalCoinQty, 8);
        document.getElementById('totalInvestedAmount').textContent = formatNumber(totalInvestedAmount, 2);
        document.getElementById('averageEntryPrice').textContent = formatNumber(averageEntryPrice, guessDecimalPlaces(averageEntryPrice));
        document.getElementById('currentPortfolioValue').textContent = formatNumber(currentPortfolioValue, 2);
        pnlAmountElement.textContent = formatNumber(pnlAmount, 2);
        pnlPercentElement.textContent = `${formatNumber(pnlPercent, 2)}%`;
        pnlAmountElement.className = 'value'; pnlPercentElement.className = 'value';
        if (pnlAmount > 0) { pnlAmountElement.classList.add('pnl-positive'); pnlPercentElement.classList.add('pnl-positive'); }
        else if (pnlAmount < 0) { pnlAmountElement.classList.add('pnl-negative'); pnlPercentElement.classList.add('pnl-negative'); }
        else { pnlAmountElement.classList.add('pnl-neutral'); pnlPercentElement.classList.add('pnl-neutral');}

        const tpP1 = parseFloat(document.getElementById('tpPercent1').value) || 0, tpP2 = parseFloat(document.getElementById('tpPercent2').value) || 0;
        const tpP3 = parseFloat(document.getElementById('tpPercent3').value) || 0, slP = parseFloat(document.getElementById('slPercent').value) || 0;
        const avgPriceDecimals = guessDecimalPlaces(averageEntryPrice);
        const tpPrice1 = averageEntryPrice > 0 ? averageEntryPrice * (1 + tpP1 / 100) : 0;
        const tpPrice2 = averageEntryPrice > 0 ? averageEntryPrice * (1 + tpP2 / 100) : 0;
        const tpPrice3 = averageEntryPrice > 0 ? averageEntryPrice * (1 + tpP3 / 100) : 0;
        const slPriceVal = averageEntryPrice > 0 && slP > 0 ? averageEntryPrice * (1 - slP / 100) : 0;
        document.getElementById('tpPrice1').textContent = formatNumber(tpPrice1, avgPriceDecimals);
        document.getElementById('tpPrice2').textContent = formatNumber(tpPrice2, avgPriceDecimals);
        document.getElementById('tpPrice3').textContent = formatNumber(tpPrice3, avgPriceDecimals);
        document.getElementById('slPrice').textContent = formatNumber(slPriceVal, avgPriceDecimals);
    }

    // --- تنسيق الأرقام ---
    function formatNumber(num, decimalPlaces = 8) {
        const number = Number(num);
        if (isNaN(number) || !isFinite(number)) return (0).toFixed(decimalPlaces);
        return number.toFixed(decimalPlaces);
    }
    // --- تخمين عدد الخانات العشرية ---
    function guessDecimalPlaces(price) {
         const num = Number(price); if (isNaN(num) || num === 0) return 2;
        if (num >= 1000) return 2; if (num >= 10) return 4; if (num >= 0.1) return 5;
        if (num >= 0.001) return 6; if (num >= 0.0001) return 7; return 8;
    }

    // --- دوال التحديث التلقائي ---
    function startAutoRefresh() {
        if (autoRefreshIntervalId) clearInterval(autoRefreshIntervalId);
        const trackedSymbols = Object.keys(allCoinData);
        if (trackedSymbols.length > 0 && autoRefreshCheckbox.checked) {
            if (!apiStatusDiv.textContent.includes('(تلقائي مفعل)')) apiStatusDiv.textContent += ' (تلقائي مفعل)';
            autoRefreshIntervalId = setInterval(() => fetchAllPrices(true), AUTO_REFRESH_INTERVAL);
        } else {
             autoRefreshCheckbox.checked = false;
             if (trackedSymbols.length === 0) {
                  apiStatusDiv.textContent = 'أضف عملة للتحديث التلقائي.'; apiStatusDiv.style.color = 'var(--text-muted)';
              }
        }
    }
    function stopAutoRefresh() {
        if (autoRefreshIntervalId) {
            clearInterval(autoRefreshIntervalId); autoRefreshIntervalId = null;
            let currentStatus = apiStatusDiv.textContent;
            currentStatus = currentStatus.replace(/\(تلقائي مفعل\)|\(تلقائي متوقف\)/g, '').trim();
             if (currentStatus.startsWith('تلقائي:')) {
                 const timePartIndex = currentStatus.indexOf('(');
                 currentStatus = timePartIndex > -1 ? currentStatus.substring(currentStatus.indexOf(':') + 1, timePartIndex).trim() : currentStatus.substring(currentStatus.indexOf(':') + 1).trim();
             }
            apiStatusDiv.textContent = currentStatus + ' (تلقائي متوقف)';
        }
    }
    function handleAutoRefreshToggle() {
        if (autoRefreshCheckbox.checked) {
            if (Object.keys(allCoinData).length > 0) { fetchAllPrices(); startAutoRefresh(); }
            else { autoRefreshCheckbox.checked = false; alert("يرجى إضافة عملة أولاً لتفعيل التحديث التلقائي."); }
        } else { stopAutoRefresh(); }
    }

    // --- التعامل مع اختيار عملة من القائمة ---
    function handleCoinSelectionChange() {
        const selectedSymbol = coinSelector.value;
        if (selectedSymbol && allCoinData[selectedSymbol]) {
            activeCoinSymbol = selectedSymbol; newCoinNameInput.value = '';
            displayCoinData(selectedSymbol); saveAllDataToLocalStorage();
        } else if (!selectedSymbol) {
             activeCoinSymbol = null; clearUIFields();
             saveAllDataToLocalStorage(); updateCoinStatus();
        } else {
             console.error(`Selected symbol ${selectedSymbol} not in data.`);
             activeCoinSymbol = null; clearUIFields(); saveAllDataToLocalStorage(); updateCoinStatus();
        }
    }

    // --- إضافة عملة جديدة أو التبديل ---
    function addOrSwitchCoin() {
        const symbol = newCoinNameInput.value.trim().toUpperCase();
        if (!symbol) { alert("يرجى إدخال رمز العملة (مثل BTCUSDT)."); return; }
        if (!/^[A-Z0-9]{3,15}$/.test(symbol)) {
            alert(`رمز العملة "${symbol}" غير صالح.`); return;
        }
        if (allCoinData[symbol]) {
            coinSelector.value = symbol; activeCoinSymbol = symbol;
            displayCoinData(symbol); saveAllDataToLocalStorage(); newCoinNameInput.value = '';
             apiStatusDiv.textContent = `تم التبديل إلى ${symbol}.`; apiStatusDiv.style.color = 'var(--text-muted)';
        } else {
            allCoinData[symbol] = getDefaultCoinDataStructure(); currentMarketPrices[symbol] = null;
            activeCoinSymbol = symbol; updateCoinSelector(); newCoinNameInput.value = '';
            saveAllDataToLocalStorage(); updateCoinStatus(); updateSummaryTable();
            alert(`تمت إضافة ${symbol}. قم بتحديث الأسعار وأدخل التفاصيل.`);
             apiStatusDiv.textContent = `تمت إضافة ${symbol}.`; apiStatusDiv.style.color = 'var(--positive-color)';
             fetchSinglePrice(symbol).then(result => {
                  if (result.price !== null) {
                      currentMarketPrices[symbol] = result.price;
                      if(activeCoinSymbol === symbol) displayCoinData(symbol); // Ensure displayCoinData is called for the newly added active coin
                      updateSummaryTable();
                      apiStatusDiv.textContent = `تم جلب سعر ${symbol}.`; apiStatusDiv.style.color = 'var(--positive-color)';
                  } else {
                       apiStatusDiv.textContent = `فشل جلب سعر ${symbol} (${result.error}).`; apiStatusDiv.style.color = 'var(--negative-color)';
                  }
             });
        }
    }

    // --- حذف العملة النشطة حاليًا ---
    function deleteCurrentCoin() {
        if (!activeCoinSymbol) { alert("لا توجد عملة محددة لحذفها."); return; }
        const symbolToDelete = activeCoinSymbol;
        if (confirm(`هل أنت متأكد أنك تريد حذف بيانات العملة ${symbolToDelete}؟`)) {
            delete allCoinData[symbolToDelete]; delete currentMarketPrices[symbolToDelete];
            activeCoinSymbol = null; updateCoinSelector(); saveAllDataToLocalStorage();
            if (!coinSelector.value) clearUIFields();
            updateSummaryTable(); updateCoinStatus();
            alert(`تم حذف العملة ${symbolToDelete}.`);
             apiStatusDiv.textContent = `تم حذف ${symbolToDelete}.`; apiStatusDiv.style.color = 'var(--text-muted)';
        }
    }

    // --- استدعاء الدوال عند تحميل الصفحة ---
    document.addEventListener('DOMContentLoaded', () => {
        loadThemePreference(); createRepurchaseRows(); loadAllDataFromLocalStorage(); updateCoinSelector();
        fetchAllPrices().then(() => {
            if (activeCoinSymbol) displayCoinData(activeCoinSymbol);
            else if (coinSelector.value && allCoinData[coinSelector.value]) { // if a coin is selected in dropdown by updateCoinSelector
                handleCoinSelectionChange();
            }
            else clearUIFields(); // Default if no active or selected coin
            if (autoRefreshCheckbox.checked) startAutoRefresh();
        });

        const inputsToSave = ['initialEntryPrice', 'initialAmountDollars', 'tpPercent1', 'tpPercent2', 'tpPercent3', 'slPercent'];
        inputsToSave.forEach(id => {
            const el = document.getElementById(id); if(el) el.addEventListener('input', saveAndCalculate);
        });
        repurchaseTableBody.addEventListener('input', (event) => {
            if (event.target && (event.target.id.startsWith('repurchasePrice') || event.target.id.startsWith('repurchaseAmount'))) {
                saveAndCalculate();
            }
        });
        autoRefreshCheckbox.addEventListener('change', handleAutoRefreshToggle);
        themeToggleBtn.addEventListener('click', toggleTheme);
        newCoinNameInput.addEventListener('keypress', (e) => { if (e.key === 'Enter') { e.preventDefault(); addOrSwitchCoin(); } });
        coinSelector.addEventListener('change', () => { if (coinSelector.value) newCoinNameInput.value = ''; }); // Clear newCoinName input when a coin is selected from dropdown

        summaryTableBody.addEventListener('click', (event) => {
            let targetCell = event.target;
            // Traverse up to find the TD if a child element was clicked
            while (targetCell && targetCell.tagName !== 'TD' && targetCell !== summaryTableBody) {
                targetCell = targetCell.parentElement;
            }

            if (targetCell && targetCell.classList.contains('summary-coin-clickable')) {
                const symbolToActivate = targetCell.dataset.symbol;
                if (symbolToActivate && allCoinData[symbolToActivate]) {
                    coinSelector.value = symbolToActivate; // Update dropdown
                    handleCoinSelectionChange(); // Process selection change
                    // Scroll to controls bar for better UX
                    const controlsBar = document.querySelector('.controls-bar');
                    if (controlsBar) controlsBar.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }
        });
    });

function downloadAllDataAsTxt() {
    if (!allCoinData || Object.keys(allCoinData).length === 0) {
        alert("لا توجد بيانات لحفظها.");
        return;
    }

    let content = "";
    for (const [symbol, data] of Object.entries(allCoinData)) {
        content += `رمز: ${symbol}\n`;
        content += `سعر الدخول: ${data.initialEntryPrice || 0}\n`;
        content += `المبلغ: ${data.initialAmountDollars || 0}\n`;

        data.repurchases.forEach((rp, i) => {
            if (rp.price && rp.amount) {
                content += `تعزيز ${i + 1}: ${rp.price}, ${rp.amount}\n`;
            }
        });

        content += `هدف1: ${data.targets.tp1 || 0}\n`;
        content += `هدف2: ${data.targets.tp2 || 0}\n`;
        content += `هدف3: ${data.targets.tp3 || 0}\n`;
        content += `وقف: ${data.targets.sl || 0}\n`;
        content += `\n`;
    }

    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const link = document.createElement('a');
    link.download = 'بيانات_العملات.txt';
    link.href = URL.createObjectURL(blob);
    link.click();
}

function handleImportTxtFile(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function (e) {
        const lines = e.target.result.split('\n');
        let symbol = null;
        let coinData = getDefaultCoinDataStructure();
        let repIndex = 0;

        lines.forEach(line => {
            line = line.trim();
            if (!line) return;

            if (line.startsWith('رمز:')) {
                if (symbol && coinData) {
                    allCoinData[symbol] = coinData;
                }
                symbol = line.split('رمز:')[1].trim();
                coinData = getDefaultCoinDataStructure();
                repIndex = 0;
            } else if (line.startsWith('سعر الدخول:')) {
                coinData.initialEntryPrice = line.split(':')[1].trim();
            } else if (line.startsWith('المبلغ:')) {
                coinData.initialAmountDollars = line.split(':')[1].trim();
            } else if (line.startsWith('تعزيز')) {
                const parts = line.split(':')[1].split(',');
                if (parts.length === 2 && repIndex < coinData.repurchases.length) {
                    coinData.repurchases[repIndex].price = parts[0].trim();
                    coinData.repurchases[repIndex].amount = parts[1].trim();
                    repIndex++;
                }
            } else if (line.startsWith('هدف1:')) {
                coinData.targets.tp1 = line.split(':')[1].trim();
            } else if (line.startsWith('هدف2:')) {
                coinData.targets.tp2 = line.split(':')[1].trim();
            } else if (line.startsWith('هدف3:')) {
                coinData.targets.tp3 = line.split(':')[1].trim();
            } else if (line.startsWith('وقف:')) {
                coinData.targets.sl = line.split(':')[1].trim();
            }
        });

        if (symbol && coinData) {
            allCoinData[symbol] = coinData;
        }

        saveAllDataToLocalStorage();
        updateCoinSelector();
        alert("تم استيراد البيانات بنجاح. اختر عملة لعرض تفاصيلها.");
    };

    reader.readAsText(file);
}

</script>

</body>
</html>